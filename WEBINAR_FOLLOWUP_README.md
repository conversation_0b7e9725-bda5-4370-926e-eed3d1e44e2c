# Webinar Follow-Up System

The webinar follow-up system allows users to automatically create follow-up meetings for webinar attendees. The process involves template assignment, host assignment (with conflict resolution), and follow-up initiation.

## Workflow Diagram

```mermaid
flowchart TD
    A[Webinar Completed] --> B[User Selects Guests]
    B --> C[User Clicks 'Set up Follow up']
    C --> D[Template Dropdown Appears]
    D --> E[User Selects Template]
    E --> F{Template Assignment}

    F -->|Success| G[All Hosts Auto-Assigned]
    F -->|Conflicts| H[Host Conflicts Detected]

    G --> M[Ready to Begin Follow-up]

    H --> I[Conflict Resolution Dialog]
    I --> J{Resolution Method}

    J -->|Manual| K[User Assigns Each Host]
    J -->|CSV| L[User Uploads CSV File]
    J -->|Round Robin| N[Auto-Distribute Hosts]

    K --> M
    L --> M
    N --> M

    M --> O{Validation Check}
    O -->|Valid| P[Begin Follow-up Process]
    O -->|Invalid| Q[Show Validation Errors]

    P --> R[Follow-up Meetings Created]
```

## API Architecture

```mermaid
flowchart TD
    subgraph "Phase 1: Template Assignment"
        A1[POST /follow-up/assign<br/>📝 Assign Template to Guests<br/>🔍 Auto-Assign Hosts & Detect Conflicts]
    end

    subgraph "Phase 2: Host Conflict Resolution"
        B1[GET /available-hosts<br/>👥 Get Available Hosts]
        B2[POST /follow-up/assign-host<br/>👤 Manual Single Assignment]
        B3[POST /follow-up/assign-hosts-csv<br/>📄 Bulk CSV Assignment]
        B4[POST /follow-up/assign-hosts-round-robin<br/>🔄 Auto-Distribute Hosts]
    end

    subgraph "Phase 3: Follow-up Initiation"
        C1[POST /follow-up/validate-begin<br/>🔍 Check Ready to Begin]
        C2[POST /follow-up/begin<br/>🚀 Start Follow-up Process]
    end

    subgraph "Phase 4: Status Monitoring"
        D1[GET /follow-up/status<br/>📊 Get All Guest Statuses]
    end

    A1 -->|Conflicts Found| B1
    A1 -->|No Conflicts| C1
    B1 --> B2
    B1 --> B3
    B1 --> B4
    B2 --> C1
    B3 --> C1
    B4 --> C1
    C1 --> C2
    C2 --> D1
```

## API Reference

### Phase 1: Template Assignment

#### 1. `POST /api/webinars/{id}/follow-up/assign`

**Purpose**: Assign follow-up template to selected guests with automatic host assignment and conflict detection

- Creates records in `webinar_followups` table
- Automatically attempts to assign hosts using routing logic (sequential/custom routing)
- Returns conflicts if host assignment fails for any guests
- **Request**: `{ templateId: number, contactIds: number[] }`
- **Response**: `{ success: boolean, conflicts?: HostConflict[] }`

**Host Assignment Logic:**

- Uses the template's team routing configuration
- Sequential routing: Round-robin assignment among team members
- Custom routing: Assignment based on contact properties and routing rules
- If routing fails, returns conflict with reason for manual resolution

### Phase 2: Host Conflict Resolution

#### 2. `GET /api/webinars/{id}/available-hosts?templateId={templateId}`

**Purpose**: Get available hosts for manual assignment

- Returns team members available for the template's team
- Used to populate host selection dropdowns in conflict resolution
- **Response**: `User[]` (array of team members)

#### 3. `POST /api/webinars/{id}/follow-up/assign-host`

**Purpose**: Manually assign specific host to specific guest

- Individual assignment for precise control
- Used in manual conflict resolution
- **Request**: `{ contactId: number, hostId: number }`
- **Response**: `{ success: boolean, message: string }`

#### 4. `POST /api/webinars/{id}/follow-up/assign-hosts-csv`

**Purpose**: Bulk assign hosts via CSV upload

- Processes CSV with guest-host mappings
- Format: `guestEmail,hostEmail` (one pair per line)
- Skips invalid entries and logs warnings
- **Request**: `{ csvData: string }`
- **Response**: `{ success: boolean, message: string }`

#### 5. `POST /api/webinars/{id}/follow-up/assign-hosts-round-robin`

**Purpose**: Auto-distribute guests among available hosts

- Ensures even workload distribution among team members
- One-click solution for conflict resolution
- **Request**: `{ contactIds: number[], templateId: number }`
- **Response**: `{ success: boolean, message: string }`

### Phase 3: Follow-up Initiation

#### 6. `POST /api/webinars/{id}/follow-up/validate-begin`

**Purpose**: Validate readiness to begin follow-up process

- Checks that webinar event date/time has elapsed
- Confirms all target guests have both templates and hosts assigned
- Verifies follow-up hasn't already been initiated
- Prevents invalid follow-up attempts
- **Request**: `{ contactIds?: number[] }` (optional - validates all guests if not provided)
- **Response**: `{ reasons: string[] }` (empty array means validation passed)

#### 7. `POST /api/webinars/{id}/follow-up/begin`

**Purpose**: Create and initiate follow-up meeting instances

- Validates prerequisites before proceeding (calls validate-begin internally)
- Creates new meeting instances using assigned templates and hosts
- Queues meetings for processing and invitation sending
- Marks follow-up as initiated to prevent duplicate runs
- **Request**: `{ contactIds?: number[] }` (optional - processes all ready guests if not provided)
- **Response**: `{ success: boolean, initiated: number }`

### Phase 4: Status Monitoring

#### 8. `GET /api/webinars/{id}/follow-up/status`

**Purpose**: Get follow-up status for all guests with assigned templates

- Shows template/host assignment status for each guest
- Indicates readiness state and any blocking issues
- Used for progress tracking, debugging, and UI state management
- **Response**: `FollowUpStatus[]` where each status includes:
  - `contactId`: Guest contact ID
  - `hasTemplate`: Whether template is assigned
  - `hasHost`: Whether host is assigned
  - `hasExistingFollowUp`: Whether follow-up has been initiated
  - `templateId`: ID of assigned template (if any)
  - `hostId`: ID of assigned host (if any)
  - `status`: Current status (`needs_template`, `needs_host`, `ready`, `staging`)

## Database Schema

### webinar_followups Table

```sql
CREATE TABLE webinar_followups (
    id BIGSERIAL PRIMARY KEY,
    meeting_id BIGINT NOT NULL REFERENCES meetings(id) ON DELETE CASCADE,
    guest_id BIGINT NOT NULL REFERENCES meeting_guest_contacts(id) ON DELETE CASCADE,
    meeting_definition_id INT REFERENCES meeting_definitions(id) ON DELETE SET NULL,
    host_id BIGINT REFERENCES users(id) ON DELETE SET NULL,
    last_error TEXT,
    initiated_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT webinar_follow_ups_meeting_guest_unique UNIQUE (meeting_id, guest_id)
);
```

**Key Points:**

- `guest_id` references `meeting_guest_contacts.id` (not `contacts.id` directly)
- `initiated_at` is set when follow-up meetings are created
- Unique constraint prevents duplicate follow-up records per guest
- Cascade deletes maintain referential integrity

### Status Calculation

Follow-up status is calculated dynamically using the `FollowUpStatusType` concrete type defined in `app/domain/bo/webinar.go`:

- `FollowUpStatusNeedsTemplate` (`needs_template`): No template assigned
- `FollowUpStatusNeedsHost` (`needs_host`): Template assigned, no host
- `FollowUpStatusReady` (`ready`): Both template and host assigned
- `FollowUpStatusStaging` (`staging`): Follow-up has been initiated

The `Status` field in both `bo.FollowUpStatus` and `webinar.FollowUpStatus` DTOs now uses `*FollowUpStatusType` instead of `*string` for better type safety.

## User Workflows

### Happy Path (No Conflicts)

1. **Select guests** → **Assign template** (API 1) → **Auto-assign hosts succeed** → **Begin follow-up** (APIs 6, 7)
2. **APIs Used**: 1 → 6 → 7 → 8 (for monitoring)

### Manual Conflict Resolution

1. **Select guests** → **Assign template** (API 1) → **Conflicts detected** → **Get available hosts** (API 2) → **Manual assignment** (API 3) → **Begin follow-up** (APIs 6, 7)
2. **APIs Used**: 1 → 2 → 3 → 6 → 7 → 8

### Bulk Conflict Resolution

1. **Select guests** → **Assign template** (API 1) → **Conflicts detected** → **Get available hosts** (API 2) → **CSV/Round-robin assignment** (APIs 4/5) → **Begin follow-up** (APIs 6, 7)
2. **APIs Used**: 1 → 2 → 4/5 → 6 → 7 → 8

### Key Flow Details

- **Template assignment (API 1)** automatically attempts host assignment using routing logic
- **Conflicts** are returned immediately if host assignment fails
- **Host resolution (APIs 2-5)** is only needed when conflicts occur
- **Validation (API 6)** is optional but recommended before beginning follow-up
- **Status monitoring (API 8)** can be used throughout the process for UI updates

## Frontend Components

### React Components

- **`FollowUpButton.tsx`** - Main follow-up initiation component with state management
- **`FollowUpTemplateDropdown.tsx`** - Template selection dropdown with filtering
- **`HostConflictResolution.tsx`** - Conflict resolution dialog with multiple resolution methods
- **`pages/events/[id]/index.tsx`** - Main webinar page integration

### API Hooks

- **`useAssignFollowUpTemplate`** - Template assignment with conflict detection
- **`useGetAvailableHosts`** - Fetch available hosts for template
- **`useAssignHost`** - Manual single host assignment
- **`useAssignHostsCSV`** - Bulk CSV host assignment
- **`useAssignHostsRoundRobin`** - Round-robin host distribution
- **`useBeginFollowUp`** - Initiate follow-up process
- **Note**: `useValidateBeginFollowUp` and `useGetFollowUpStatus` hooks are not currently implemented in frontend

## Implementation Notes

### Backend Architecture

- **Host assignment** leverages existing team routing logic (`NextUserForWebinarFollowup`)
- **Sequential routing**: Round-robin assignment among team members
- **Custom routing**: Assignment based on contact properties and routing rules
- **Service layer** handles all business logic; repository layer manages database operations
- **Conflict handling** returns structured errors with contact details and failure reasons

### Data Flow

- **Template assignment** creates `webinar_followups` records with automatic host assignment
- **Guest IDs** are `meeting_guest_contacts.id` (not direct `contacts.id`)
- **Status calculation** is dynamic based on template/host presence and initiation state
- **Follow-up initiation** creates new meeting instances and queues them for processing

### Frontend Integration

- **Follow-up templates** must be calendar or email invite types only
- **Conflict resolution** provides three methods: manual, CSV upload, round-robin
- **State management** uses SWR for caching and automatic revalidation
- **UI updates** happen automatically through cache invalidation

### System Safeguards

- **Duplicate prevention**: `initiated_at` field prevents multiple follow-up runs
- **Validation checks**: Event elapsed, hosts assigned, no existing follow-ups
- **Idempotent operations**: Template assignment uses UPSERT, host assignment is repeatable
- **Error handling**: Structured error responses with actionable conflict information

### Technical Details

- **Round-robin** distributes only conflicted guests, not all guests
- **CSV format**: `guestEmail,hostEmail` (one pair per line)
- **All APIs** include proper error handling and logging
- **Swagger documentation** included for all endpoints
