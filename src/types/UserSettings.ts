import { WeekSchedule } from "./Availability";

export type UserSettings = {
  firstName: string;
  lastName: string;
  meetingLink: string;
  signature: string;
  timezone: string;
  title: string;
  location: string;
  calendars: Array<{
    calendarId: string;
    calendarType: string;
    description: string;
    id: number;
    isPublic: boolean;
    isUsedForAvailability: boolean;
    lastUpdated: string;
    name: string;
    timezone: string;
  }> | null;
  notifications: {
    kronologicInvite: {
      rhi: boolean;
    };
    kronologicEmail: {
      accept: boolean;
      decline: boolean;
      rhi: boolean;
    };
  };
  eventColors: Array<{
    name: string;
    displayColor: string;
  }> | null;
  defaultEventColor: string;
  weekSchedule: WeekSchedule;
};
