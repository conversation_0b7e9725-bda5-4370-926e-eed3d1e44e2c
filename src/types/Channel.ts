import { Tag } from "./Tag";
import { MeetingDefinition } from "./MeetingDefinition";

export interface Integration {
  id: number;
  name: string;
}

export interface ChannelWithDetails {
  id: number;
  name: string;
  type: string;
  description: string;
  enabled: boolean;
  config: string;
  createdAt: string; //FIXME: make this a ts?
  tags: Tag[];
  integration: Integration;
  meetingTemplate: MeetingDefinition; // FIXME: should use SkinnyMeetingDefinition instead
  meetingTemplateID: number;
}
export type Channel = Pick<
  ChannelWithDetails,
  "id" | "name" | "type" | "tags" | "enabled"
>;
