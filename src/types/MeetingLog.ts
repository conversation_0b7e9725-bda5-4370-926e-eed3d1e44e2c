import { MeetingStatus } from "./MeetingStatus";

interface Event {
  timestamp: string;
  customerLogType: string;
  structuredData: {
    eventStart: string;
    statusNew: MeetingStatus;
    statusOld: MeetingStatus;
    userFirstName: string;
    userLastName: string;
    meetingId: number;
    userEmail: string;
    contactFirstName: string;
    contactLastName: string;
    contactEmail: string;
  };
}

export type MeetingLog = Event[];
