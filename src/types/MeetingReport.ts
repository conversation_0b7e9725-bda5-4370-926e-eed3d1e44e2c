import { GuestStatus } from "./GuestStatus";
import { CoHostFilterOption } from "../componentsV2/filters/CoHostFilter";

export interface MeetingReport {
  readyToSend: {
    total: number;
    actionRequired: number;
    draftErrors: number;
    draft: number;
    queued: number;
    queuingErrors: number;
  };
  scheduling: {
    total: number;
    scheduling: number;
    awaitingResponse: number;
    aiNegotiating: number;
    away: number;
    hostActionNeeded: number;
    hostIntervened: number;
  };
  scheduled: {
    total: number;
  };
  finalized: {
    total: number;
    finalized: number;
    acceptanceRate: number;
    completed: number;
    noQuorum: number;
    accepted: number;
    declined: number;
    respondedUnknownIntent: number;
    noResponse: number;
    cancelled: number;
  };
}

export interface MeetingReportQuery {
  filter: {
    guestEmail: string;
    hostEmail: string;
    notes: string;
    guestStatusFilter: GuestStatus[];
    meetingTypeFilter: number[] | undefined;
    tagFilter: number[];
    meetingIdFilter: number[];
    coHostFilter: CoHostFilterOption | null;
    dateFilter?: {
      end?: string; // ex. 2023-03-24T23:59:59
      start?: string; // ex. 2023-03-24T23:59:59
      timezone?: string;
    };
    lastActivityFilter?: {
      end?: string; // ex. 2023-03-24T23:59:59
      start?: string; // ex. 2023-03-24T23:59:59
      timezone?: string;
    };
  };
}
