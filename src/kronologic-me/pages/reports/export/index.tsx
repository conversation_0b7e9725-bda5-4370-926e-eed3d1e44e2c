import { mdiAccountOutline } from "@mdi/js";
import Icon from "@mdi/react";
import { Grid } from "@mui/material";
import { ActionButton } from "src/componentsV2/ActionButton";
import { PageLayout } from "src/componentsV2/PageLayout";

function KMeExport() {
  return (
    <PageLayout
      title="Exports"
      breadcrumbs={[
        { name: "Reports", route: "/reports" },
        { name: "Export", route: "/reports/export" },
      ]}
    >
      <Grid
        container
        sx={{ justifyContent: "center", alignItems: "center", gap: 4 }}
      >
        <Grid item>
          <ActionButton
            title="Meetings"
            icon={<Icon path={mdiAccountOutline} size={3} />}
            to="/reports/export/meetings"
          />
        </Grid>
        <Grid item>
          <ActionButton
            title="Contacts"
            icon={<Icon path={mdiAccountOutline} size={3} />}
            to="/reports/export/contacts"
          />
        </Grid>
      </Grid>
    </PageLayout>
  );
}

export default KMeExport;
