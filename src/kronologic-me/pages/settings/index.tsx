import { mdiAccount, mdiInformation } from "@mdi/js";
import Icon from "@mdi/react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Container,
  Grid,
  Link,
  Stack,
  Typography,
  useTheme,
} from "@mui/material";
import { NavLink, Redirect, Route, Switch } from "react-router-dom";
import useGeneralNotifications from "src/hooks/useGeneralNotifications";
import useBillingPortal from "src/kronologic-me/mutations/useBilling";
import Accounts from "src/pages/settings/accounts";
import AddNewAccount from "src/pages/settings/accounts/add";
import SettingsBuild from "src/pages/settings/build";
import KMeManageAccount from "./accounts/[slug]";
import BusinessIcon from "@mui/icons-material/Business";
import { useHasOrgAdminPermissions } from "../../../auth";
import OrgSettings from "../../../pages/settings/org-settings";

export default function Settings() {
  const { addError } = useGeneralNotifications();
  const fetchBillingUrl = useBillingPortal();
  const hasOrgAdminPermissions = useHasOrgAdminPermissions();

  const onBillingClick = async () => {
    try {
      const data = await fetchBillingUrl();
      window.location.href = data.url;
    } catch (error) {
      if (error instanceof Error) {
        addError(error.message);
      }
    }
  };

  const theme = useTheme();

  return (
    <Container maxWidth="xl" sx={{ mt: 2 }}>
      <Grid container spacing={4}>
        <Grid item xs={3}>
          <Card>
            <CardHeader title={"Settings"} />
            <CardContent>
              <section>
                <Stack sx={{ gap: 2 }}>
                  <Link
                    component={NavLink}
                    to="/settings/accounts"
                    style={{
                      textDecoration: "none",
                      color: theme.palette.text.primary,
                    }}
                    activeStyle={{ color: theme.palette.primary.main }}
                  >
                    <Stack
                      sx={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 1,
                      }}
                    >
                      <Icon path={mdiAccount} size={1.3} />
                      <Typography>Accounts</Typography>
                    </Stack>
                  </Link>
                  <Link
                    component={NavLink}
                    to="/settings/build"
                    style={{
                      textDecoration: "none",
                      color: theme.palette.text.primary,
                    }}
                    activeStyle={{ color: theme.palette.primary.main }}
                  >
                    <Stack
                      sx={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 1,
                      }}
                    >
                      <Icon path={mdiInformation} size={1.3} />
                      <Typography>Build Information</Typography>
                    </Stack>
                  </Link>
                  {hasOrgAdminPermissions && (
                    <Link
                      component={NavLink}
                      to="/settings/org-settings"
                      style={{
                        textDecoration: "none",
                        color: theme.palette.text.primary,
                      }}
                      activeStyle={{ color: theme.palette.primary.main }}
                    >
                      <Stack
                        sx={{
                          flexDirection: "row",
                          alignItems: "center",
                          gap: 1,
                        }}
                      >
                        <BusinessIcon
                          sx={{
                            fontSize: "30px",
                          }}
                        />
                        <Typography>Organization Settings</Typography>
                      </Stack>
                    </Link>
                  )}
                  <Link
                    onClick={onBillingClick}
                    style={{
                      textDecoration: "none",
                      cursor: "pointer",
                      color: theme.palette.text.primary,
                    }}
                  >
                    <Stack
                      sx={{
                        flexDirection: "row",
                        alignItems: "center",
                        gap: 1,
                      }}
                    >
                      <Icon path={mdiInformation} size={1.3} />
                      <Typography>Manage Subscription</Typography>
                    </Stack>
                  </Link>
                </Stack>
              </section>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={9}>
          <Switch>
            <Route
              path="/settings/accounts/add"
              render={() => <AddNewAccount />}
            />
            <Route
              path="/settings/accounts/:userId"
              render={() => <KMeManageAccount />}
            />
            <Route path="/settings/accounts" render={() => <Accounts />} />
            <Route path="/settings/build" render={() => <SettingsBuild />} />
            {hasOrgAdminPermissions && (
              <Route
                path="/settings/org-settings"
                render={() => <OrgSettings />}
              />
            )}
            <Route
              path="*"
              render={() => <Redirect to="/settings/accounts" />}
            />
          </Switch>
        </Grid>
      </Grid>
    </Container>
  );
}
