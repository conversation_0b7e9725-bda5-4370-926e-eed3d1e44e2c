import { ContactWithDetails, MeetingDefinition, Role, User } from "src/types";

export interface State {
  guest: ContactWithDetails | null;
  host: {
    id: number;
    role: Role;
    firstName: string;
    lastName: string;
    email: string;
    title?: string;
    location?: string;
  } | null;
  meetingType: MeetingDefinition | null;
  overrides: {
    gap: number;
    duration: number;
    bufferDurationMinutes: number | null;
    dayRangeFrom: number;
    dayRangeTo: number;
    emailSubjectTemplate: string | null;
    emailBodyTemplate: string | null;
    inviteTitleTemplate: string | null;
    inviteBodyTemplate: string | null;
  };
  startTime: string | null;
  preferredTimeType: preferredTimeType;
}

type preferredTimeType = "fixed time" | "scheduling range";
