import useSWR from "swr";
import { useUserService } from "../../services";
import { useTokenRefreshHandler } from "../../hooks";
import { useActingAsOverrideHeader } from "../../auth";
import { getUserToken } from "src/utils/jwtToken";
import { errorHandler } from "src/hooks/errorHandler";

interface SubscriptionStatus {
  active: boolean;
  billingPeriodEnd: number;
}

export function useUserSubscription() {
  const service = useUserService();
  const accessToken = getUserToken();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const url = `/api/users/me/subscription`;

  const { data, error } = useSWR(url, (u) =>
    service
      .get(u)
      .set(headers)
      .then(tokenRefresh<PERSON>and<PERSON>)
      .then((res: Response) => res.body)
      .catch(errorHandler),
  );

  return {
    data: { data } as { data: SubscriptionStatus },
    error,
    loading: !error && !data,
  };
}

export default useUserSubscription;
