/**
 * Utility functions for working with dates
 */
import dayjs from "dayjs";
import { Dayjs } from "dayjs";

/**
 * Checks if a value is a valid Dayjs object with a valid date
 * @param val Any value to check
 * @returns true if the value is a valid Dayjs object with a valid date
 */
export const isValidDayjsDate = (val: any): boolean => {
  if (typeof val === "string") {
    const dayjsDate = dayjs(val);
    return isValidDayjsDate(dayjsDate);
  }

  if (
    !val ||
    typeof val.isValid !== "function" ||
    typeof val.year !== "function"
  ) {
    return false;
  }

  if (!val.isValid()) {
    return false;
  }

  const year = val.year();

  if (year === 0 || (year === 1 && val.month() === 0 && val.date() === 1)) {
    return false;
  }

  if (year < 1900 || year > 2100) {
    return false;
  }

  return true;
};

/**
 * Creates a default time set to the current date at 12:00 PM
 * @returns A Dayjs object representing the current date at 12:00 PM
 */
export const getDefaultNoonTime = (): Dayjs => {
  return dayjs().hour(12).minute(0).second(0);
};
