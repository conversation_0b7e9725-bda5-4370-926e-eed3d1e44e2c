//============================================================
// General
//============================================================

@function v($var, $fallback: null) {
  @if ($fallback) {
    @return var(--#{$var}, #{$fallback});
  } @else {
    @return var(--#{$var});
  }
}

// replace substring with another string
@function str-replace($string, $search, $replace: "") {
  $index: str-index($string, $search);
  @if $index {
    @return str-slice($string, 1, $index - 1) + $replace +
      str-replace(
        str-slice($string, $index + str-length($search)),
        $search,
        $replace
      );
  }
  @return $string;
}

//============================================================
// Colors
//============================================================

// return css color variable with different opacity value
@function alpha($color, $opacity) {
  $color: str-replace($color, "var(");
  $color: str-replace($color, ")");
  $color-h: var(#{$color + "-h"});
  $color-s: var(#{$color + "-s"});
  $color-l: var(#{$color + "-l"});
  @return hsla($color-h, $color-s, $color-l, $opacity);
}

// Requires inline-block or block for proper styling
@mixin text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

//============================================================
// Transforms
//============================================================

@mixin transform($arguments...) {
  -webkit-transform: $arguments;
  -ms-transform: $arguments; // IE9
  transform: $arguments;
}

@mixin transform-origin($arguments...) {
  -webkit-transform-origin: $arguments;
  -ms-transform-origin: $arguments; // IE9
  transform-origin: $arguments;
}

@mixin rotate($arguments...) {
  -webkit-transform: rotate($arguments);
  -ms-transform: rotate($arguments); // IE9
  transform: rotate($arguments);
}

@mixin backface-visibility($arguments) {
  -webkit-backface-visibility: $arguments;
  -moz-backface-visibility: $arguments;
  -ms-backface-visibility: $arguments;
  -o-backface-visibility: $arguments;
  backface-visibility: $arguments;
}

//============================================================
// Animations
//============================================================

@mixin transition($arguments...) {
  -webkit-transition: $arguments;
  transition: $arguments;
}

@mixin animation($arguments) {
  -webkit-animation: $arguments;
  animation: $arguments;
}

//============================================================
// Gradients
//============================================================

@mixin linear-gradient($from, $to) {
  background-color: $from;
  background-image: -webkit-linear-gradient(
    top,
    $from,
    $to
  ); /* Chrome 10-25, iOS 5+, Safari 5.1+ */
  background-image: linear-gradient(
    to bottom,
    $from,
    $to
  ); /* Chrome 26, Firefox 16+, IE 10+, Opera 12.10+ */
}

//============================================================
// Structure
//============================================================

@mixin box-sizing($arguments) {
  -webkit-box-sizing: $arguments;
  -moz-box-sizing: $arguments;
  -o-box-sizing: $arguments;
  box-sizing: $arguments;
}

//============================================================
//
// Easing
//
// Thanks to Robert Penner for his sterling work on easing,
// and to Matthew Lein for converting these functions into
// approximated cubic-bezier functions. Respect.
//
// @see http://robertpenner.com/
// @see http://matthewlein.com/ceaser/
//
//============================================================

// Cubic
$easeInCubic: cubic-bezier(0.55, 0.055, 0.675, 0.19);
$easeOutCubic: cubic-bezier(0.215, 0.61, 0.355, 1);
$easeInOutCubic: cubic-bezier(0.645, 0.045, 0.355, 1);

// Circ
$easeInCirc: cubic-bezier(0.6, 0.04, 0.98, 0.335);
$easeOutCirc: cubic-bezier(0.075, 0.82, 0.165, 1);
$easeInOutCirc: cubic-bezier(0.785, 0.135, 0.15, 0.86);

// Expo
$easeInExpo: cubic-bezier(0.95, 0.05, 0.795, 0.035);
$easeOutExpo: cubic-bezier(0.19, 1, 0.22, 1);
$easeInOutExpo: cubic-bezier(1, 0, 0, 1);

// Quad
$easeInQuad: cubic-bezier(0.55, 0.085, 0.68, 0.53);
$easeOutQuad: cubic-bezier(0.25, 0.46, 0.45, 0.94);
$easeInOutQuad: cubic-bezier(0.455, 0.03, 0.515, 0.955);

// Quart
$easeInQuart: cubic-bezier(0.895, 0.03, 0.685, 0.22);
$easeOutQuart: cubic-bezier(0.165, 0.84, 0.44, 1);
$easeInOutQuart: cubic-bezier(0.77, 0, 0.175, 1);

// Quint
$easeInQuint: cubic-bezier(0.755, 0.05, 0.855, 0.06);
$easeOutQuint: cubic-bezier(0.23, 1, 0.32, 1);
$easeInOutQuint: cubic-bezier(0.86, 0, 0.07, 1);

// Sine
$easeInSine: cubic-bezier(0.47, 0, 0.745, 0.715);
$easeOutSine: cubic-bezier(0.39, 0.575, 0.565, 1);
$easeInOutSine: cubic-bezier(0.445, 0.05, 0.55, 0.95);

// Back
$easeInBack: cubic-bezier(0.6, -0.28, 0.735, 0.045);
$easeOutBack: cubic-bezier(0.175, 0.885, 0.32, 1.275);
$easeInOutBack: cubic-bezier(0.68, -0.55, 0.265, 1.55);
