import React from "react";
import { ButtonContainer } from "../../components/button";
import style from "./style.module.scss";

export function ConfigurationButton({ name, onClick, disabled }) {
  return (
    <ButtonContainer
      name={name}
      onClick={onClick}
      disabled={disabled}
      secondary
      small
      transparent
      className={style.integrationCard_configurationButton}
    >
      Advanced Configuration
    </ButtonContainer>
  );
}
