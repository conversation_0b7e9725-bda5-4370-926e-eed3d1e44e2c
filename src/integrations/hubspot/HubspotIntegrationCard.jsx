import React, { useCallback, useMemo } from "react";

import { Logo } from "./Logo";
import { INTEGRATION_AUTH_STATUS } from "../props";
import {
  ComingSoon,
  DeleteButton,
  ReauthorizeButton,
  ConnectButton,
  DisconnectButton,
} from "../buttons";
import {
  IntegrationCard,
  IntegrationCardButtonArea,
  IntegrationCardTitle,
} from "../IntegrationCard";
import { ConnectionStatus } from "../ConnectionStatus";
import { determineIntegrationStatus } from "../util";
import style from "./style.module.scss";

export function HubspotIntegrationCard({
  enabled,
  onConnect,
  onDisconnect,
  integration = null,
  isLoading,
}) {
  const connected = integration !== null;
  const authStatus = determineIntegrationStatus(integration);

  const connectionStatus = useMemo(
    () => `Auth Status: ${authStatus}`,
    [authStatus],
  );

  // reauthorize existing integration
  const reauthorize = useCallback(() => {
    onConnect(integration.id);
  }, [integration, onConnect]);

  // connect new integration
  const connect = useCallback(() => {
    onConnect(0);
  }, [onConnect]);

  return (
    <IntegrationCard enabled={enabled}>
      {connected && <ConnectionStatus>{connectionStatus}</ConnectionStatus>}
      <Logo />

      <IntegrationCardTitle>Hubspot</IntegrationCardTitle>

      <IntegrationCardButtonArea>
        {isLoading && <span>Loading Integrations...</span>}
        {enabled &&
          !connected &&
          authStatus === INTEGRATION_AUTH_STATUS.DISABLED && (
            <>
              <ConnectButton
                name="/integrations/hubspot/connect"
                onClick={connect}
              />
            </>
          )}

        {enabled &&
          connected &&
          authStatus === INTEGRATION_AUTH_STATUS.EXPIRED && (
            <div className={style.reauth_container}>
              <ReauthorizeButton
                name="/integrations/hubspot/reauthorize"
                onClick={reauthorize}
              />
              <DeleteButton
                name="/integrations/hubspot/delete"
                onClick={onDisconnect}
              />
            </div>
          )}

        {enabled &&
          connected &&
          authStatus === INTEGRATION_AUTH_STATUS.CURRENT && (
            <DisconnectButton
              name="/integrations/hubspot/disconnect"
              onClick={onDisconnect}
            />
          )}
        {!enabled && <ComingSoon name="/integrations/hubspot/coming_soon" />}
      </IntegrationCardButtonArea>
    </IntegrationCard>
  );
}
