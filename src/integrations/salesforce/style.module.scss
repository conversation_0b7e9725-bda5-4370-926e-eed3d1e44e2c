:local {
  [data-theme="default"] {
    --integrations-info-txt-color: hsl(0, 0%, 30%);
    --integrations-env-url-input: var(--neutral-300);
    --integrations-env-url-input-color: var(--neutral-500);
    --integrations-env-url-input-box-shadow: var(--neutral-400);
  }

  [data-theme="dark"] {
    --integrations-info-txt-color: hsl(0, 0%, 60%);
    --integrations-env-url-input: var(--neutral-700);
    --integrations-env-url-input-color: var(--neutral-500);
    --integrations-env-url-input-box-shadow: var(--neutral-900);
  }

  .logo {
    width: 100px;
    height: 100px;
  }

  .salesforceConfigModal__container {
    max-width: 28em;
  }

  .salesforceConfigModal__containerParentDiv {
    display: flex;
  }

  .salesforceConfigModal__containerChildDiv {
    width: 1em;
  }

  .salesforceConfigModal__textArea {
    min-width: 25em;
    width: 100%;
  }

  .link {
    color: var(--summer-sky-color);
  }

  .sf_input {
    border-radius: 0px 4px 4px 0px;
    width: 100%;
  }

  .reauth_container {
    width: 90%;
    display: flex;
  }
}
