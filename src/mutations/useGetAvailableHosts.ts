import useS<PERSON> from "swr";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { errorHandler } from "src/hooks/errorHandler";

export interface AvailableHost {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
}

export function useGetAvailableHosts(eventId: number, templateId: number) {
  const accessToken = getUserToken();
  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const { data, error } = useSWR(
    eventId && templateId
      ? `/api/webinars/${eventId}/available-hosts?templateId=${templateId}`
      : null,
    (url: string) =>
      service
        .get(url)
        .set(headers)
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body)
        .catch(errorHandler),
  );

  return {
    data: data as AvailableHost[] | undefined,
    error,
    loading: !error && !data,
  };
}

export default useGetAvailableHosts;
