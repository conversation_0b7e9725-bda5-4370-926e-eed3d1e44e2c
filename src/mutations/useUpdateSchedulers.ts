import { useSWRConfig } from "swr";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useUserService } from "../services";
import { errorHandler } from "src/hooks/errorHandler";
import { WeekSchedule } from "src/types/Availability";

export function useUpdateSchedulers() {
  const accessToken = getUserToken();
  const { mutate, cache } = useSWRConfig();

  const service = useUserService();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  return (weekSchedule: WeekSchedule, userIds: number[]): Promise<void> =>
    service
      .patch(`/api/app/settings/schedulers`)
      .set(headers)
      .send({
        weekSchedule,
        userIds,
      })
      .then((res: Response) => res.body)
      .then((res: any) => {
        // @ts-ignore
        const keys = cache.keys();

        // Invalidate meeting queries and force refresh.
        Array.from(keys)
          .filter((r: any) => r.includes("/api/users"))
          .forEach((k: any) => {
            mutate(k);
          });

        return res;
      })
      .catch(errorHandler);
}

export default useUpdateSchedulers;
