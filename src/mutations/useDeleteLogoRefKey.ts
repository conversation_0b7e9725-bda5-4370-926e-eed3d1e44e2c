import { useSWRConfig } from "swr";
import { useActingAsOverrideHeader } from "../auth";
import { useUserService } from "../services";
import { useTokenRefreshHandler } from "../hooks";
import { getUserToken } from "../utils/jwtToken";
import { errorHandler } from "src/hooks/errorHandler";

export function useDeleteLogoRefKey() {
  const accessToken = getUserToken();
  const { mutate, cache } = useSWRConfig();

  const service = useUserService();
  const override = useActingAsOverrideHeader();
  const tokenRefreshHandler = useTokenRefreshHandler();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  return async (): Promise<void> =>
    service
      .delete(`/api/org/setting/logo`)
      .set(headers)
      .then(tokenRefreshHandler)
      .then((res: Response) => res.body)
      .then((res: Response) => {
        // @ts-ignore - `keys` is not explicitly declared on type `cache`
        const keys: IterableIterator<string> = cache.keys();

        Array.from(keys)
          .filter((route: string) => route.startsWith("/api/org/setting"))
          .forEach((route: string) => {
            mutate(route);
          });

        return res;
      })
      .catch(errorHandler);
}
