import { useSWRConfig } from "swr";
import { ResponseError } from "superagent";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { errorHandler } from "src/hooks/errorHandler";

export interface AssignFollowUpTemplateRequest {
  eventId: number;
  templateId: number;
  contactIds: number[];
}

export interface HostConflict {
  contactId: number;
  contactEmail: string;
  reason: string;
}

export interface AssignFollowUpTemplateResponse {
  success: boolean;
  conflicts?: HostConflict[];
}

export function useAssignFollowUpTemplate() {
  const accessToken = getUserToken();
  const { mutate, cache } = useSWRConfig();

  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  return async (
    request: AssignFollowUpTemplateRequest,
  ): Promise<AssignFollowUpTemplateResponse> => {
    try {
      const response = await service
        .post(`/api/webinars/${request.eventId}/follow-up/assign`)
        .set(headers)
        .send({
          templateId: request.templateId,
          contactIds: request.contactIds,
        })
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body);

      // Invalidate webinar queries to refresh the data
      // @ts-ignore
      const keys = cache.keys();
      Array.from(keys)
        .filter((r: any) => r.includes("/api/webinars"))
        .forEach((k: any) => {
          mutate(k);
        });

      return response;
    } catch (error) {
      throw errorHandler(error as ResponseError);
    }
  };
}

export default useAssignFollowUpTemplate;
