import { useSWRConfig } from "swr";
import { ResponseError } from "superagent";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { errorHandler } from "src/hooks/errorHandler";

export interface AssignHostRequest {
  eventId: number;
  contactId: number;
  hostId: number;
}

export function useAssignHost() {
  const accessToken = getUserToken();
  const { mutate, cache } = useSWRConfig();

  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  return async (request: AssignHostRequest): Promise<{ success: boolean }> => {
    try {
      const response = await service
        .post(`/api/webinars/${request.eventId}/follow-up/assign-host`)
        .set(headers)
        .send({
          contactId: request.contactId,
          hostId: request.hostId,
        })
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body);

      // Invalidate webinar queries to refresh the data
      // @ts-ignore
      const keys = cache.keys();
      Array.from(keys)
        .filter((r: any) => r.includes("/api/webinars"))
        .forEach((k: any) => {
          mutate(k);
        });

      return response;
    } catch (error) {
      throw errorHandler(error as ResponseError);
    }
  };
}

export default useAssignHost;
