import React from "react";
import cx from "classnames";
import { Switch } from "../switch";
import {
  INITIALIZING_FAULT_STAGE,
  isFinalMeetingStage,
  isMeetingCancelledStage,
  isMeetingInitFaultStage,
} from "../../meetings/props";
import style from "./style.module.scss";

export function MeetingActivationSwitch({
  handleToggle,
  meeting,
  name,
  disabled,
}) {
  const activationSwitchDisabled =
    disabled ||
    !meeting.guests ||
    !meeting.meetingType?.active ||
    meeting.status === INITIALIZING_FAULT_STAGE ||
    isFinalMeetingStage(meeting.status) ||
    isMeetingCancelledStage(meeting.status) ||
    (isMeetingInitFaultStage(meeting.status) && !meeting.enabled);

  const readyToActivate = !activationSwitchDisabled && !meeting.enabled;

  return (
    <Switch
      isOn={meeting.enabled}
      handleToggle={handleToggle}
      labelClassName={cx({
        [style["activeSwitch__container--readyToActivate"]]: readyToActivate,
      })}
      name={name}
      disabled={activationSwitchDisabled}
    />
  );
}
