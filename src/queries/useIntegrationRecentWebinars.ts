import useS<PERSON> from "swr";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { buildQueryString } from "../utils";
import { errorHandler } from "src/hooks/errorHandler";
import { IntegrationWebinarEvent } from "src/types/IntegrationWebinarEvent";

export function useIntegrationRecentWebinars(integrationName: string) {
  const service = useUserService();
  const accessToken = getUserToken();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const q = buildQueryString({
    integrationName,
  });

  const { data, error } = useSWR(
    `/api/integrations/webinar/recent?${q}`,
    (url: string) =>
      service
        .get(url)
        .set(headers)
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body)
        .catch(errorHandler),
  );

  return {
    data: data as IntegrationWebinarEvent[] | undefined,
    error,
    loading: !error && !data,
  };
}
