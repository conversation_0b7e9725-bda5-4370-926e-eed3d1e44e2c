import useS<PERSON> from "swr";

import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { errorHandler } from "src/hooks/errorHandler";
import { useUserService } from "../services";
import { ChannelWithDetails } from "../types";

export function useChannel(id: number | string) {
  const service = useUserService();
  const accessToken = getUserToken();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  // ################################
  // FIXME: fix route to be /api/user/channels/{channelType}/{id}
  // ################################
  const url = `/api/user/channels/import/${id}`;

  const { data, error, mutate } = useSWR(url, (u) =>
    service
      .get(u)
      .set(headers)
      .then(tokenRefreshHandler)
      .then((res: Response) => res.body)
      .catch(errorHandler),
  );

  return {
    data: data as ChannelWithDetails,
    error,
    loading: !error && !data,
    mutate,
  };
}
