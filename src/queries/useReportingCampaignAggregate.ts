import useS<PERSON> from "swr";
import { useUserService } from "../services";
import { useTokenRefreshHandler } from "../hooks";
import { useActingAsOverrideHeader } from "../auth";
import { getUserToken } from "../utils/jwtToken";
import { AggregateData } from "./useReportingCampaign";
import { errorHandler } from "src/hooks/errorHandler";

type ReportingCampaignAggregateRequest = {
  dateFrom?: string;
  dateTo?: string;
  meetingDefinitionIds?: number[];
  team?: number[];
  meetingTags?: number[];
  templateTags?: number[];
  user?: number[];
};

export function useReportingCampaignAggregate(
  query: ReportingCampaignAggregateRequest,
) {
  const service = useUserService();
  const accessToken = getUserToken();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const { data, error } = useSWR(
    ["/api/reports/campaign/aggregate", query],
    (url: string, body: object) =>
      service
        .post(url)
        .set(headers)
        .send(body)
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body)
        .catch(errorHandler),
  );

  return {
    data: data as AggregateData,
    error,
    loading: !error && !data,
  };
}
