import useSWRImmutable from "swr/immutable";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { MeetingDefinition } from "../types";
import { errorHandler } from "src/hooks/errorHandler";

export function useMeetingDefinition(id?: number | string) {
  const accessToken = getUserToken();
  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const { data, error } = useSWRImmutable(
    id ? `/api/meetings/definition/${id}` : null,
    (url: string) =>
      service
        .get(url)
        .set(headers)
        .then(tokenRefresh<PERSON>andler)
        .then((res: Response) => res.body)
        .catch(errorHandler),
    {
      revalidateOnMount: true,
    },
  );

  return {
    data: data as MeetingDefinition | undefined,
    error,
    loading: !error && !data,
  };
}
