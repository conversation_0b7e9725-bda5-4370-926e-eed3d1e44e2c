import useSWR from "swr";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { ContactWithDetails } from "../types";
import { errorHandler } from "src/hooks/errorHandler";

export function useContact(id: number | string) {
  const service = useUserService();
  const accessToken = getUserToken();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const url = `/api/contact/${id}`;

  const { data, error, mutate } = useSWR(url, (u) =>
    service
      .get(u)
      .set(headers)
      .then(tokenRefreshHand<PERSON>)
      .then((res: Response) => res.body)
      .catch(errorHandler),
  );

  return {
    data: data as ContactWithDetails,
    error,
    loading: !error && !data,
    mutate,
  };
}
