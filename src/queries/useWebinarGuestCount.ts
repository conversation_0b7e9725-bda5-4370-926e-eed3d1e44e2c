import useS<PERSON> from "swr";

import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useUserService } from "../services";
import { useTokenRefreshHandler } from "src/hooks";
import { errorHandler } from "src/hooks/errorHandler";

export interface WebinarGuestCounts {
  total: number;
  acceptedTotal: number;
  declinedTotal: number;
  noResponseTotal: number;
  awaitingResponseTotal: number;
}

export function useWebinarGuestCount(webinarID: string) {
  const accessToken = getUserToken();
  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const { data, error } = useSWR(
    `/api/webinars/${webinarID}/guest-counts`,
    (url: string) =>
      service
        .get(url)
        .set(headers)
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body)
        .catch(errorHandler),
  );

  return {
    data: data as WebinarGuestCounts | undefined,
    error,
    loading: !error && !data,
  };
}
