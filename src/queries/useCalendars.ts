import useSWR from "swr";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useUserService } from "../services";
import { Calendar } from "../types";
import { errorHandler } from "src/hooks/errorHandler";

export function useCalendars() {
  const service = useUserService();
  const accessToken = getUserToken();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const { data, error } = useSWR(`/api/calendars`, (u) =>
    service
      .get(u)
      .set(headers)
      .then((res: Response) => res.body)
      .catch(errorHandler),
  );

  // The API returns null if the list of calendars is empty. We will mask this
  // behavior here until the API can be updated appropriately.
  const d = data === null ? [] : data;

  return {
    data: d as Calendar[] | undefined,
    error,
    loading: !error && !d,
  };
}

export default useCalendars;
