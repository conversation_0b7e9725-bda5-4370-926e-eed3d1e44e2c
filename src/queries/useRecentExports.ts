import useSWR from "swr";
import { getUserToken } from "src/utils/jwtToken";
import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { DataExport } from "../types";
import { errorHandler } from "src/hooks/errorHandler";

export function useRecentExports() {
  const service = useUserService();
  const accessToken = getUserToken();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const url = `/api/exports`;

  const { data, error } = useSWR(
    url,
    (u) =>
      service
        .get(u)
        .set(headers)
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body)
        .catch(errorHandler),
    {
      refreshInterval: 10000,
    },
  );

  return {
    data: data as { data: DataExport[] },
    error,
    loading: !error && !data,
  };
}
