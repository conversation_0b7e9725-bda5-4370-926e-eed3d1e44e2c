import { useActingAsOverrideHeader } from "../auth";
import { useTokenRefreshHandler } from "../hooks";
import { useUserService } from "../services";
import { getUserToken } from "../utils/jwtToken";
import { errorHandler } from "src/hooks/errorHandler";
import { ExploJWT } from "../types/Explo";
import useSWRImmutable from "swr/immutable";

export function useGetExploJWTToken() {
  const accessToken = getUserToken();
  const service = useUserService();
  const tokenRefreshHandler = useTokenRefreshHandler();
  const override = useActingAsOverrideHeader();

  const headers: { [index: string]: string } = {
    "JWT-TOKEN": accessToken as string,
  };

  if (override) {
    headers.override = override;
  }

  const { data, error } = useSWRImmutable(
    "/api/explo/dashboard/token",
    (url: string) =>
      service
        .post(url)
        .set(headers)
        .then(tokenRefreshHandler)
        .then((res: Response) => res.body)
        .catch(errorHandler),
  );

  return {
    data: data as ExploJWT,
    error,
    loading: !error && !data,
  };
}
