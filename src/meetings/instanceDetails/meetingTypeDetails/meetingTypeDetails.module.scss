:local {
  [data-theme="default"] {
    --meeting-type-details-background: #d5dee8;
    --invite-text-color: #1f3049;
  }

  [data-theme="dark"] {
    --meeting-type-details-background: rgba(255, 255, 255, 0.1);
    --invite-text-color: var(--neutral-white);
  }

  @supports (backdrop-filter: blur(6px)) {
    .meetingTypeDetails {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
      background: var(--meeting-type-details-background);
      backdrop-filter: blur(6px);
    }
  }

  @supports not (backdrop-filter: blur(6px)) {
    .meetingTypeDetails {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 99;
      background: var(--meeting-type-details-background);
    }
  }

  .meetingTypeDetails__invite {
    padding: 10px 10%;
  }

  .meetingTypeDetails_invite__textarea {
    width: 100%;
    height: auto;
    background-color: transparent;
    color: var(--invite-text-color);
    border: none;
    overflow: scroll;
    resize: none;
    font-family: var(--font-family);
    font-size: 0.8em;
    font-style: normal;
    font-weight: normal;
    margin-bottom: 10px;
  }

  .meetingTypeDetails_invite__expand_button {
    border: none;
    background-color: transparent;
    color: var(--invite-text-color);
    font-weight: normal;
  }

  .meetingTypeDetails_invite_expand_button__icon {
    color: var(--invite-text-color);
  }
}
