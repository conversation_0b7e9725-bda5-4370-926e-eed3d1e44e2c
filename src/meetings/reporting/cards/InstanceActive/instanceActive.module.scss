@import "../../../../style/mixins";
@import "../cards";

:local {
  [data-theme="default"] {
    --instance-active-card-by-color: inheirt;
  }

  [data-theme="dark"] {
    --instance-active-card-by-color: #0c0a25;
  }

  .instanceActive {
    flex: 2 2 50%;
    padding: 5px;
  }

  .instanceActive__card {
    background-color: var(--instance-active-card-by-color);
  }

  .instanceActive__card_content {
    min-height: 140px;
    padding: 0 25%;
  }

  .instanceActive__text {
    flex: 3 3 75%;
    padding-bottom: $text-above-value-padding;
  }

  .instanceActive__text_total {
    flex: 3 3 75%;
    padding: 0 5% $text-above-value-padding 0;
  }

  .instanceActive__value {
    color: $text-color-instance-stages;
    flex: 1 1 25%;
    justify-content: flex-start;
    padding-bottom: $text-above-value-padding;

    &:hover {
      @include hover-underline;
    }
  }
}
