import React, { useState } from "react";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import CircularProgress from "@mui/material/CircularProgress";
import { ListItem, ListItemText } from "@mui/material";

import { useDebounce } from "../../hooks";

import { User } from "../../types";
import { useSelectCoHost } from "../../queries/useSelectCoHost";

export function CoHostSelectInput({
  required,
  error,
  helperText,
  coHost,
  onCoHostSelect,
}: {
  required?: boolean;
  error?: boolean;
  helperText?: string;
  coHost: User | null;
  onCoHostSelect: (value: User | null) => void;
}) {
  const [open, setOpen] = useState(false);
  const [input, setInput] = useState("");

  const qry = useDebounce(input, 750);

  const { data, loading } = useSelectCoHost(100, 0, qry);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Autocomplete
      open={open}
      disablePortal
      onOpen={handleOpen}
      onClose={handleClose}
      inputValue={input}
      onInputChange={(_, value) => {
        setInput(value);
      }}
      onChange={(_, value) => {
        onCoHostSelect(value);
      }}
      value={coHost}
      isOptionEqualToValue={(option, value) => option.email === value.email}
      getOptionLabel={(option) => option.email}
      renderOption={(props, option) => (
        <ListItem key={option.id} {...props} dense>
          <ListItemText
            primary={`${option.firstName} ${option.lastName}`}
            secondary={option.email}
          />
        </ListItem>
      )}
      options={data?.data || []}
      loading={loading}
      renderInput={(params) => (
        <TextField
          required={required}
          error={error}
          helperText={helperText}
          variant="outlined"
          {...params}
          label="Co-Host"
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <>
                {loading ? (
                  <CircularProgress color="inherit" size={20} />
                ) : null}
              </>
            ),
          }}
        />
      )}
    />
  );
}
