import { useEffect } from "react";
import { Box, Stack, TextField } from "@mui/material";
import {
  DatePicker,
  LocalizationProvider,
  TimePicker,
} from "@mui/x-date-pickers-pro";
import { AdapterDayjs } from "@mui/x-date-pickers-pro/AdapterDayjs";
import { Dayjs } from "dayjs";
import { isValidDayjsDate } from "src/utils/dateUtils";
import dayjs from "dayjs";

function MeetingTimePicker({
  value,
  onChange,
  onValidationChange,
}: {
  value: Dayjs | null;
  onChange: (newValue: Dayjs | null) => void;
  onValidationChange?: (isValid: boolean) => void;
}) {
  const today = dayjs().startOf("day").add(12, "hour");
  const displayValue = isValidDayjsDate(value) ? value : today;

  // Set default value on mount if no valid value is provided
  useEffect(() => {
    if (!isValidDayjsDate(value)) {
      onChange(today);
      onValidationChange?.(true);
    }
  }, []);

  const handleChange = (v: Dayjs | null) => {
    if (!v || (v && typeof v.isValid === "function" && !v.isValid())) {
      onChange(value);
      return;
    }

    onChange(v);
  };

  return (
    <LocalizationProvider
      dateAdapter={AdapterDayjs}
      localeText={{
        start: "Start",
        end: "End",
      }}
    >
      <Stack spacing={1}>
        <Stack direction="row" alignItems="center">
          <DatePicker
            label="Date"
            disablePast
            value={displayValue}
            onChange={handleChange}
            renderInput={(props) => <TextField {...props} />}
          />
          <Box sx={{ mx: 2 }}> at </Box>
          <TimePicker
            label="Time"
            value={displayValue}
            onChange={handleChange}
            renderInput={(props) => <TextField {...props} />}
          />
        </Stack>
      </Stack>
    </LocalizationProvider>
  );
}

export default MeetingTimePicker;
