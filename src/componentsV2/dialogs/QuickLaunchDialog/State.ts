import {
  ContactWithDetails,
  MeetingDefinition,
  Role,
  User,
} from "../../../types";
import { MeetingHost } from "../../inputs";

export interface State {
  guest: ContactWithDetails | null;
  host: MeetingHost | null;
  coHost: User | null;
  meetingType: MeetingDefinition | null;
  overrides: {
    gap: number;
    duration: number;
    bufferDurationMinutes: number | null;
    dayRangeFrom: number;
    dayRangeTo: number;
    emailSubjectTemplate: string | null;
    emailBodyTemplate: string | null;
    inviteTitleTemplate: string | null;
    inviteBodyTemplate: string | null;
  };
  startTime: string | null;
  preferredTimeType: preferredTimeType;
  actingAsUserRole: Role;
}

type preferredTimeType = "fixed time" | "scheduling range";
