import { TokenStatus, TokenStatusOptions } from "src/types";
import FilterDropdown from "./FilterDropdown";

export function CalendarConnectionFilter(props: {
  value?: TokenStatus[];
  onChange?: (value: TokenStatus[]) => void;
}) {
  return (
    <FilterDropdown
      label="Calendar Connection"
      value={props.value}
      onChange={props.onChange}
      options={TokenStatusOptions}
    />
  );
}

export default CalendarConnectionFilter;
