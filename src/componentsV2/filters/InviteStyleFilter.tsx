import { InviteStyle, InviteStyleOptions } from "src/types/InviteStyle";
import FilterSelect from "./FilterSelect";

export function InviteStyleFilter(props: {
  value?: InviteStyle | null;
  onChange?: (value: InviteStyle | null) => void;
}) {
  return (
    <FilterSelect
      label="Invite Style"
      value={props.value}
      onChange={props.onChange}
      options={InviteStyleOptions}
    />
  );
}

export default InviteStyleFilter;
