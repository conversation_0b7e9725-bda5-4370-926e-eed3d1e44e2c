import { Box, Button, Stack } from "@mui/material";
import { Tag } from "src/types";
import FilterListIcon from "@mui/icons-material/FilterList";
import TagFilter from "../importExport/export/filters/TagFilter";

type ChannelsFilterBarProps = {
  selectedTags: Tag[];
  setSelectedTags: (value: Tag[]) => void;
  onReset: () => void;
};

export const ChannelFilterBar = (props: ChannelsFilterBarProps) => {
  return (
    <Stack direction="row" sx={{ marginBottom: "20px" }}>
      <FilterListIcon
        color="primary"
        sx={{ marginBottom: "auto", marginTop: "auto" }}
      />
      <Box
        sx={{
          marginBottom: "auto",
          marginLeft: "10px",
          marginTop: "auto",
          width: "33%",
        }}
      >
        <Box
          sx={{
            marginBottom: "auto",
            marginLeft: "10px",
            marginRight: "10px",
            marginTop: "auto",
            width: "300px",
          }}
        >
          <TagFilter
            value={props.selectedTags}
            onChange={props.setSelectedTags}
          />
        </Box>

        <Button
          sx={{
            marginBottom: "auto",
            marginTop: "auto",
            textTransform: "none",
          }}
          onClick={() => {
            props.setSelectedTags([]);
          }}
        >
          Reset
        </Button>
      </Box>
    </Stack>
  );
};
