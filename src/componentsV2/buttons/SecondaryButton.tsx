import React from "react";
import { Button } from "@mui/material";
import { ButtonProps } from "./ButtonProps";

function SecondaryButton({
  disabled,
  nowrap,
  icon,
  children,
  onClick,
  size,
  ...rest
}: ButtonProps & {
  icon?: React.ReactNode;
  nowrap?: boolean;
  children?: string;
}) {
  return (
    <Button
      size={size}
      variant="outlined"
      color="primary"
      startIcon={icon}
      disabled={disabled}
      onClick={onClick}
      style={{
        minWidth: "fit-content",
        textTransform: "none",
        whiteSpace: nowrap ? "nowrap" : "normal",
      }}
      {...rest}
    >
      {children}
    </Button>
  );
}

export default SecondaryButton;
