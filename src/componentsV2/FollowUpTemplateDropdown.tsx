import React, { useState, useEffect, useRef } from "react";
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
  Chip,
  ListItemText,
  Box,
} from "@mui/material";
import { CalendarMonth, MailOutline } from "@mui/icons-material";
import { useMeetingTemplates, SkinnyMeetingDefinition } from "src/queries";
import { INVITE_STYLE } from "src/meetingTypes/invite/props";
import Loading from "./Loading";

interface FollowUpTemplateDropdownProps {
  value?: SkinnyMeetingDefinition | null;
  onChange: (template: SkinnyMeetingDefinition | null) => void;
  disabled?: boolean;
  onCreateNewTemplate?: () => void;
  refreshTrigger?: number; // Optional prop to force refresh
}

export function FollowUpTemplateDropdown({
  value,
  onChange,
  disabled = false,
  onCreateNewTemplate,
  refreshTrigger,
}: FollowUpTemplateDropdownProps) {
  const [open, setOpen] = useState(false);
  const selectRef = useRef<HTMLDivElement>(null);

  // Fetch templates filtered for invite and email types only
  const { data, loading, error, mutate } = useMeetingTemplates(100, 0, {
    inviteStyle: [INVITE_STYLE.CALENDAR_FIRST, INVITE_STYLE.CUSTOM_INVITE],
    status: ["active"], // Only show active templates
  });

  // Force refresh when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger) {
      mutate();
    }
  }, [refreshTrigger, mutate]);

  // Sort templates by creation date (newest first) to ensure newly created templates appear at the top
  const templates = (data?.data || []).sort((a, b) => {
    const dateA = new Date(a.creationData?.createdAt || 0);
    const dateB = new Date(b.creationData?.createdAt || 0);
    return dateB.getTime() - dateA.getTime();
  });

  const handleChange = (event: SelectChangeEvent<string>) => {
    const templateId = event.target.value;
    if (templateId === "") {
      onChange(null);
    } else if (templateId === "create-new" && onCreateNewTemplate) {
      onCreateNewTemplate();
      setOpen(false);
    } else {
      const selectedTemplate = templates.find(
        (template) => template.id.toString() === templateId,
      );
      onChange(selectedTemplate || null);
    }
  };

  const getTemplateIcon = (inviteStyle: string) => {
    switch (inviteStyle) {
      case INVITE_STYLE.CALENDAR_FIRST:
        return <CalendarMonth sx={{ fontSize: 16, mr: 1 }} />;
      case INVITE_STYLE.CUSTOM_INVITE:
        return <MailOutline sx={{ fontSize: 16, mr: 1 }} />;
      default:
        return null;
    }
  };

  const getTemplateTypeLabel = (inviteStyle: string) => {
    switch (inviteStyle) {
      case INVITE_STYLE.CALENDAR_FIRST:
        return "Kronologic Invite";
      case INVITE_STYLE.CUSTOM_INVITE:
        return "Kronologic Email";
      default:
        return "Unknown";
    }
  };

  if (loading) {
    return <Loading />;
  }

  if (error) {
    return (
      <Typography color="error">
        Error loading templates. Please try again.
      </Typography>
    );
  }

  return (
    <FormControl fullWidth disabled={disabled} ref={selectRef}>
      <InputLabel id="follow-up-template-label">Assign Template</InputLabel>
      <Select
        labelId="follow-up-template-label"
        value={value?.id.toString() || ""}
        label="Assign Template"
        onChange={handleChange}
        open={open}
        onOpen={() => setOpen(true)}
        onClose={() => setOpen(false)}
        renderValue={(selected) => {
          if (!selected) return "";
          const template = templates.find((t) => t.id.toString() === selected);
          return template ? template.name : "";
        }}
      >
        <MenuItem value="">
          <em>Select a template</em>
        </MenuItem>
        {onCreateNewTemplate && (
          <MenuItem
            value="create-new"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              setOpen(false);
              onChange(null);
              setTimeout(() => {
                onCreateNewTemplate();
              }, 100);
            }}
          >
            <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
              <Typography sx={{ fontWeight: "bold", color: "primary.main" }}>
                + Create New Template
              </Typography>
            </Box>
          </MenuItem>
        )}
        {templates.map((template) => (
          <MenuItem key={template.id} value={template.id.toString()}>
            <Box sx={{ display: "flex", alignItems: "center", width: "100%" }}>
              {getTemplateIcon(template.inviteStyle)}
              <ListItemText
                primary={template.name}
                secondary={!template.active ? "Inactive" : undefined}
              />
              <Chip
                size="small"
                label={getTemplateTypeLabel(template.inviteStyle)}
                sx={{ ml: 1 }}
                color={
                  template.inviteStyle === INVITE_STYLE.CALENDAR_FIRST
                    ? "primary"
                    : "secondary"
                }
              />
            </Box>
          </MenuItem>
        ))}
        {templates.length === 0 && (
          <MenuItem disabled>
            <em>No templates available</em>
          </MenuItem>
        )}
      </Select>
    </FormControl>
  );
}

export default FollowUpTemplateDropdown;
