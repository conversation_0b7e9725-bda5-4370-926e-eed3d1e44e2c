import React, { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>ack } from "@mui/material";
import { Dayjs } from "dayjs";

import { SkinnyMeetingDefinition } from "src/queries";
import FilterSearchString from "./filters/FilterSearchString";
import TemplateFilter from "./filters/TemplateFilter";
import TagFilter from "./filters/TagFilter";
import { Tag } from "src/types";
import DateRangeFilterForWebinars from "./filters/DateRangeFilterForWebinars";
import { INVITE_STYLE } from "src/meetingTypes/invite/props";

type WebinarsFilterBarProps = {
  filters: WebinarQuery;
  templateFilter: number[];
  onFilterUpdate: (filters: WebinarQuery) => void;
  onTemplateFilterUpdate: (templates: SkinnyMeetingDefinition[]) => void;
  onReset: () => void;
};

function WebinarsFilterBar(props: WebinarsFilterBarProps) {
  const [query, setQuery] = useState<WebinarQuery>(props.filters);

  useEffect(() => {
    setQuery(props.filters);
  }, [props.filters]);

  const [updateTimeout, setUpdateTimeout] = useState<any>();

  // We have to maintain an extra state because meetingIdFilter accepts a number array.
  const [meetingIdString, setMeetingIdString] = useState("");

  const updateFilters = (newFilters: Partial<WebinarQuery>) => {
    clearTimeout(updateTimeout);

    setQuery((oldFilters) => {
      const filtersToSet = { ...oldFilters, ...newFilters };
      const newTimeout = setTimeout(
        () => props.onFilterUpdate(filtersToSet),
        750,
      );

      setUpdateTimeout(newTimeout);

      return filtersToSet;
    });
  };

  return (
    <Stack direction="row" spacing={2} alignItems="center">
      <TemplateFilter
        search={query.templateFilterSearch}
        onSearchChange={(value) => {
          updateFilters({
            templateFilterSearch: value,
          });
        }}
        value={props.templateFilter}
        onChange={(templates) => {
          props.onTemplateFilterUpdate(templates);
          updateFilters({
            template: templates,
          });
        }}
        inviteStyleFilter={[INVITE_STYLE.WEBINAR]}
      />
      <TagFilter
        search={query.tagFilterSearch}
        onSearchChange={(value) =>
          updateFilters({
            tagFilterSearch: value,
          })
        }
        value={query.tags}
        onChange={(tags) =>
          updateFilters({
            tags,
          })
        }
      />
      <FilterSearchString
        label="Host Email"
        value={query.hostEmail}
        onChange={(value) => updateFilters({ hostEmail: value })}
      />
      <DateRangeFilterForWebinars
        value={[query.date.range[0], query.date.range[1]]}
        onChange={(range) =>
          updateFilters({
            date: { ...query.date, range },
          })
        }
      />
      <Divider orientation="vertical" flexItem />
      <Button
        onClick={() => {
          props.onReset();
          setMeetingIdString("");
        }}
      >
        Reset
      </Button>
    </Stack>
  );
}

export default WebinarsFilterBar;

export interface WebinarQuery {
  hostEmail: string;
  meetingStatusFilter: string[];
  meetingIdFilter: number[];
  templateIds?: number[];
  tags: Tag[];
  tagFilterSearch: string;
  templateFilterSearch: string;
  template: SkinnyMeetingDefinition[];
  date: {
    field: "startTime";
    range: [Dayjs | null, Dayjs | null];
  };
  paging: { limit: number; offset: number };
  sort: { field: "meetingTime" | "lastActivity"; order: "asc" | "desc" };
}
