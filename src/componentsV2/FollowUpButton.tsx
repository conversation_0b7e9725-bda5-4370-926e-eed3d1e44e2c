import { useState } from "react";
import { Stack, <PERSON>po<PERSON>, <PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { Warning } from "@mui/icons-material";
import PrimaryButton from "./buttons/PrimaryButton";
import SecondaryButton from "./buttons/SecondaryButton";
import FollowUpTemplateDropdown from "./FollowUpTemplateDropdown";
import HostConflictResolution from "./HostConflictResolution";
import { CreateTemplateModal } from "./dialogs";
import { SkinnyMeetingDefinition } from "src/queries";
import { Guest } from "src/types";
import {
  useAssignFollowUpTemplate,
  HostConflict,
} from "src/mutations/useAssignFollowUpTemplate";
import { useGetAvailableHosts } from "src/mutations/useGetAvailableHosts";

interface FollowUpButtonProps {
  eventId: number;
  selectedGuests: Guest[];
  onTemplateAssign: (template: SkinnyMeetingDefinition) => void;
  onBeginFollowUp: () => void;
  hasAssignedTemplates: boolean;
  disabled?: boolean;
}

export function FollowUpButton({
  eventId,
  selectedGuests,
  onTemplateAssign,
  onBeginFollowUp,
  hasAssignedTemplates,
  disabled = false,
}: FollowUpButtonProps) {
  const [showTemplateDropdown, setShowTemplateDropdown] = useState(false);
  const [selectedTemplate, setSelectedTemplate] =
    useState<SkinnyMeetingDefinition | null>(null);
  const [hostConflictError, setHostConflictError] = useState<string | null>(
    null,
  );
  const [hostConflicts, setHostConflicts] = useState<HostConflict[]>([]);
  const [showConflictResolution, setShowConflictResolution] = useState(false);
  const [showCreateTemplateModal, setShowCreateTemplateModal] = useState(false);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  const assignFollowUpTemplate = useAssignFollowUpTemplate();
  const { data: availableHosts = [] } = useGetAvailableHosts(
    eventId,
    selectedTemplate?.id || 0,
  );

  const handleSetUpFollowUp = () => {
    if (selectedGuests.length === 0) {
      return;
    }
    setShowTemplateDropdown(true);
  };

  const handleTemplateSelect = async (
    template: SkinnyMeetingDefinition | null,
  ) => {
    setSelectedTemplate(template);
    setHostConflictError(null);

    if (template) {
      try {
        // Attempt to assign the template - this will return conflicts if any
        const assignmentResult = await assignFollowUpTemplate({
          eventId,
          contactIds: selectedGuests.map((guest) => guest.id),
          templateId: template.id,
        });

        if (
          assignmentResult.conflicts &&
          assignmentResult.conflicts.length > 0
        ) {
          const conflictCount = assignmentResult.conflicts.length;
          const message =
            conflictCount === 1
              ? "One of the assigned contacts could not be matched with a host."
              : `${conflictCount} of the assigned contacts could not be matched with a host.`;
          setHostConflictError(message);
          setHostConflicts(assignmentResult.conflicts);
          return;
        }

        // If assignment was successful without conflicts, proceed
        onTemplateAssign(template);
        setShowTemplateDropdown(false);
        setSelectedTemplate(null);
      } catch (error) {
        console.error("Error assigning follow-up template:", error);
        setHostConflictError(
          "Unable to assign follow-up template. Please try again.",
        );
      }
    }
  };

  const handleBackToGuestSelection = () => {
    setShowTemplateDropdown(false);
    setSelectedTemplate(null);
    setHostConflictError(null);
  };

  const handleFixHostConflicts = () => {
    setShowConflictResolution(true);
  };

  const handleCreateNewTemplate = () => {
    setShowCreateTemplateModal(true);
  };

  const handleTemplateCreated = (newTemplate: SkinnyMeetingDefinition) => {
    setSelectedTemplate(newTemplate);
    setShowCreateTemplateModal(false);
    setRefreshTrigger((prev) => prev + 1); // Trigger dropdown refresh
    // Small delay to ensure the template is fully saved before proceeding
    setTimeout(() => {
      handleTemplateSelect(newTemplate);
    }, 100);
  };

  const handleConflictResolved = () => {
    setHostConflictError(null);
    setHostConflicts([]);
    if (selectedTemplate) {
      onTemplateAssign(selectedTemplate);
      setShowTemplateDropdown(false);
      setSelectedTemplate(null);
    }
  };

  return (
    <>
      <Stack spacing={2} sx={{ width: "100%", maxWidth: "350px" }}>
        {showTemplateDropdown ? (
          <>
            <FollowUpTemplateDropdown
              value={selectedTemplate}
              onChange={handleTemplateSelect}
              disabled={disabled}
              onCreateNewTemplate={handleCreateNewTemplate}
              refreshTrigger={refreshTrigger}
            />

            <SecondaryButton onClick={handleBackToGuestSelection}>
              Back
            </SecondaryButton>

            {hostConflictError && (
              <Alert
                severity="warning"
                icon={<Warning />}
                action={
                  <Button
                    size="small"
                    onClick={handleFixHostConflicts}
                    sx={{ textDecoration: "underline" }}
                  >
                    Fix This
                  </Button>
                }
              >
                <Typography variant="body2">{hostConflictError}</Typography>
              </Alert>
            )}
          </>
        ) : (
          <>
            {!hasAssignedTemplates ? (
              <PrimaryButton
                onClick={handleSetUpFollowUp}
                disabled={disabled || selectedGuests.length === 0}
                fullWidth
              >
                Set up Follow Up
              </PrimaryButton>
            ) : (
              <PrimaryButton
                onClick={onBeginFollowUp}
                disabled={disabled}
                fullWidth
              >
                Begin Follow Up
              </PrimaryButton>
            )}
          </>
        )}
      </Stack>

      {/* Modals rendered outside the main container to avoid z-index issues */}
      <HostConflictResolution
        open={showConflictResolution}
        onClose={() => setShowConflictResolution(false)}
        eventId={eventId}
        templateId={selectedTemplate?.id || 0}
        conflicts={hostConflicts}
        availableHosts={availableHosts}
        onResolved={handleConflictResolved}
      />

      <CreateTemplateModal
        open={showCreateTemplateModal}
        onClose={() => setShowCreateTemplateModal(false)}
        onTemplateCreated={handleTemplateCreated}
      />
    </>
  );
}

export default FollowUpButton;
