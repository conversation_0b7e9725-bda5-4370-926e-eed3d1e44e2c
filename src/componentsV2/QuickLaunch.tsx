import { RocketLaunch } from "@mui/icons-material";
import { atom, useAtom } from "jotai";
import PrimaryButton from "./buttons/PrimaryButton";
import { QuickLaunchDialog } from "./dialogs";

export const showQuickLaunchAtom = atom(false);

function QuickLaunch({ disabled }: { disabled?: boolean }) {
  const [showQuickLaunch, setShowQuickLaunch] = useAtom(showQuickLaunchAtom);

  return (
    <>
      <PrimaryButton
        icon={<RocketLaunch />}
        disabled={disabled}
        onClick={() => setShowQuickLaunch(true)}
      >
        Launcher
      </PrimaryButton>

      <QuickLaunchDialog
        dialog
        open={showQuickLaunch}
        onClose={() => setShowQuickLaunch(false)}
      />
    </>
  );
}

export default QuickLaunch;
