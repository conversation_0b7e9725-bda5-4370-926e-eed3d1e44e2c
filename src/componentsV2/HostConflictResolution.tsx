import { useState } from "react";
import {
  DialogContent,
  DialogActions,
  Typography,
  <PERSON>ack,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  TextField,
  Alert,
  <PERSON>,
  Box,
} from "@mui/material";
import Dialog from "./dialogs/Dialog";
import { LoadingButton } from "@mui/lab";
import { Upload, AutoFix<PERSON>igh, Person } from "@mui/icons-material";
import SecondaryButton from "./buttons/SecondaryButton";
import { HostConflict } from "src/mutations/useAssignFollowUpTemplate";
import { AvailableHost } from "src/mutations/useGetAvailableHosts";
import { useAssignHost } from "src/mutations/useAssignHost";
import { useAssignHostsCSV } from "src/mutations/useAssignHostsCSV";
import { useAssignHostsRoundRobin } from "src/mutations/useAssignHostsRoundRobin";

interface HostConflictResolutionProps {
  open: boolean;
  onClose: () => void;
  eventId: number;
  templateId: number;
  conflicts: HostConflict[];
  availableHosts: AvailableHost[];
  onResolved: () => void;
}

export function HostConflictResolution({
  open,
  onClose,
  eventId,
  templateId,
  conflicts,
  availableHosts,
  onResolved,
}: HostConflictResolutionProps) {
  const [resolutionMethod, setResolutionMethod] = useState<
    "manual" | "csv" | "round-robin"
  >("manual");
  const [manualAssignments, setManualAssignments] = useState<
    Record<number, number>
  >({});
  const [csvData, setCsvData] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const assignHost = useAssignHost();
  const assignHostsCSV = useAssignHostsCSV();
  const assignHostsRoundRobin = useAssignHostsRoundRobin();

  const handleManualAssignment = (contactId: number, hostId: number) => {
    setManualAssignments((prev) => ({
      ...prev,
      [contactId]: hostId,
    }));
  };

  const handleResolveConflicts = async () => {
    setLoading(true);
    setError(null);

    try {
      if (resolutionMethod === "manual") {
        // Assign hosts manually for each conflict
        for (const conflict of conflicts) {
          const hostId = manualAssignments[conflict.contactId];
          if (hostId) {
            await assignHost({
              eventId,
              contactId: conflict.contactId,
              hostId,
            });
          }
        }
      } else if (resolutionMethod === "csv") {
        await assignHostsCSV({
          eventId,
          csvData,
        });
      } else if (resolutionMethod === "round-robin") {
        await assignHostsRoundRobin({
          eventId,
          templateId,
          contactIds: conflicts.map((c) => c.contactId),
        });
      }

      onResolved();
      onClose();
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to resolve conflicts",
      );
    } finally {
      setLoading(false);
    }
  };

  const canResolve = () => {
    if (resolutionMethod === "manual") {
      return conflicts.every(
        (conflict) => manualAssignments[conflict.contactId],
      );
    } else if (resolutionMethod === "csv") {
      return csvData.trim().length > 0;
    } else if (resolutionMethod === "round-robin") {
      return availableHosts.length > 0;
    }
    return false;
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      title="Resolve Host Conflicts"
    >
      <DialogContent>
        <Stack spacing={3}>
          <Alert severity="warning">
            {conflicts.length} guest{conflicts.length > 1 ? "s" : ""} could not
            be automatically assigned hosts. Please choose a resolution method
            below.
          </Alert>

          <FormControl fullWidth>
            <InputLabel>Resolution Method</InputLabel>
            <Select
              value={resolutionMethod}
              onChange={(e) => setResolutionMethod(e.target.value as any)}
              label="Resolution Method"
            >
              <MenuItem value="manual">
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Person fontSize="small" />
                  <span>Manual Assignment</span>
                </Stack>
              </MenuItem>
              <MenuItem value="csv">
                <Stack direction="row" alignItems="center" spacing={1}>
                  <Upload fontSize="small" />
                  <span>CSV Upload</span>
                </Stack>
              </MenuItem>
              <MenuItem value="round-robin">
                <Stack direction="row" alignItems="center" spacing={1}>
                  <AutoFixHigh fontSize="small" />
                  <span>Round Robin</span>
                </Stack>
              </MenuItem>
            </Select>
          </FormControl>

          {resolutionMethod === "manual" && (
            <Stack spacing={2}>
              <Typography variant="subtitle2">
                Assign hosts manually:
              </Typography>
              {conflicts.map((conflict) => (
                <Box
                  key={conflict.contactId}
                  sx={{
                    p: 2,
                    border: 1,
                    borderColor: "divider",
                    borderRadius: 1,
                  }}
                >
                  <Stack spacing={2}>
                    <Stack direction="row" alignItems="center" spacing={1}>
                      <Chip label={conflict.contactEmail} size="small" />
                      <Typography variant="body2" color="text.secondary">
                        {conflict.reason}
                      </Typography>
                    </Stack>
                    <FormControl fullWidth size="small">
                      <InputLabel>Assign Host</InputLabel>
                      <Select
                        value={manualAssignments[conflict.contactId] || ""}
                        onChange={(e) =>
                          handleManualAssignment(
                            conflict.contactId,
                            Number(e.target.value),
                          )
                        }
                        label="Assign Host"
                      >
                        {availableHosts.map((host) => (
                          <MenuItem key={host.id} value={host.id}>
                            {host.firstName} {host.lastName} ({host.email})
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Stack>
                </Box>
              ))}
            </Stack>
          )}

          {resolutionMethod === "csv" && (
            <Stack spacing={2}>
              <Typography variant="subtitle2">
                Upload CSV with guest email and host email pairs:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Format: guestEmail,hostEmail (one pair per line)
              </Typography>
              <TextField
                multiline
                rows={6}
                placeholder="<EMAIL>,<EMAIL>&#10;<EMAIL>,<EMAIL>"
                value={csvData}
                onChange={(e) => setCsvData(e.target.value)}
                fullWidth
              />
            </Stack>
          )}

          {resolutionMethod === "round-robin" && (
            <Stack spacing={2}>
              <Typography variant="subtitle2">
                Assign hosts using round-robin distribution:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Guests will be automatically assigned to available hosts in
                rotation. Available hosts: {availableHosts.length}
              </Typography>
              {availableHosts.length === 0 && (
                <Alert severity="error">
                  No available hosts found. Please use manual assignment or CSV
                  upload.
                </Alert>
              )}
            </Stack>
          )}

          {error && <Alert severity="error">{error}</Alert>}
        </Stack>
      </DialogContent>

      <DialogActions>
        <SecondaryButton onClick={onClose} disabled={loading}>
          Cancel
        </SecondaryButton>
        <LoadingButton
          onClick={handleResolveConflicts}
          disabled={!canResolve() || loading}
          loading={loading}
          variant="contained"
          color="primary"
        >
          Resolve Conflicts
        </LoadingButton>
      </DialogActions>
    </Dialog>
  );
}

export default HostConflictResolution;
