import React, { useState, useRef } from "react";
import { Box, Chip, Stack, Typography } from "@mui/material";

import ImportFooter from "../ImportFooter";
import { ContactUpload } from "./ContactUpload";
import { MeetingDefinition, Tag } from "src/types";
import { useUploadContacts } from "src/mutations";
import { useUpdateMeetingTemplate } from "src/mutations/useUpdateMeetingTemplate";
import useGeneralNotifications from "src/hooks/useGeneralNotifications";
import { Loading } from "src/componentsV2/Loading";

export function Confirmation({
  contacts,
  meetingTemplate,
  tags,
  onPrev,
  onNext,
  setDisableSave,
}: {
  contacts: ContactUpload;
  meetingTemplate: MeetingDefinition | null;
  tags: Tag[];
  onPrev: () => void;
  onNext: () => void;
  setDisableSave?: (value: boolean) => void;
}) {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const mounted = useRef(true);
  const { addError } = useGeneralNotifications();
  const uploadContacts = useUploadContacts();
  const updateMeetingTemplate = useUpdateMeetingTemplate();

  const handleSubmit = async () => {
    if (loading) return;
    setLoading(true);
    try {
      // First save template if it exists
      if (meetingTemplate && mounted.current) {
        await updateMeetingTemplate(meetingTemplate.id, {
          ...meetingTemplate,
          enabled: meetingTemplate.active,
          team: meetingTemplate.team.id,
          routingJustMe: meetingTemplate.routingJustMe,
          tags: meetingTemplate.tags.map((tag) => tag.id),
          routing:
            meetingTemplate.routing === "email"
              ? "custom"
              : meetingTemplate.routing,
        });
      }

      // Then upload contacts
      const uploadResult = await uploadContacts(
        contacts,
        meetingTemplate?.id,
        tags?.map((tag) => tag.id),
      );

      // If everything succeeded, move to next step
      if (mounted.current) {
        onNext();
      }
    } catch (error: any) {
      console.error("Error in handleSubmit:", error);
      if (mounted.current) {
        setError(true);
        addError(`Operation failed: ${error.message}`);
      }
    } finally {
      if (mounted.current) {
        setLoading(false);
      }
    }
  };

  if (loading) {
    return <Loading />;
  }

  if (error) {
    return (
      <Box sx={{ width: "100%", minHeight: "400px" }}>
        <Stack spacing={2}>
          <Typography align="center">
            There was an error when uploading the contacts.
          </Typography>
          <ImportFooter
            onLeftButtonClick={onPrev}
            onRightButtonClick={handleSubmit}
            rightButtonText="Try Again"
            rightButtonDisabled={loading}
          />
        </Stack>
      </Box>
    );
  }

  return (
    <Box sx={{ width: "100%", minHeight: "400px" }}>
      <Stack alignItems="center" spacing={2}>
        <Typography align="center">
          Let's make sure everything looks right
        </Typography>

        <Typography align="center">
          You will be uploading {contacts.length}{" "}
          {contacts.length === 1 ? "contact" : "contacts"}
          {meetingTemplate === null
            ? ". You have not selected a Meeting Template. No new meetings will be created. You may go back if you wish to change this."
            : ` and creating a ${meetingTemplate.name} meeting with them.`}
        </Typography>

        {meetingTemplate !== null && (!tags || tags.length < 1) && (
          <Typography align="center">
            The meetings will not be tagged.
          </Typography>
        )}

        {meetingTemplate !== null && tags && tags.length > 0 && (
          <Stack spacing={1} justifyContent="center" alignItems="center">
            <Typography align="center">
              The meetings will be created with the following tags:
            </Typography>
            <Stack direction="row" spacing={0.5} sx={{ width: "fit-content" }}>
              {tags.map((t) => (
                <Chip size="small" key={t.id} label={t.name} />
              ))}
            </Stack>
          </Stack>
        )}
      </Stack>
      <ImportFooter
        onLeftButtonClick={onPrev}
        onRightButtonClick={handleSubmit}
        rightButtonText="Submit"
        rightButtonDisabled={loading}
      />
    </Box>
  );
}

export default Confirmation;
