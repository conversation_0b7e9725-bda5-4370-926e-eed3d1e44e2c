// Lets create a type that potentially has all the properties of a contact but is garunteed to have email

import { ContactWithDetails } from "src/types";

// (the minimum needed to define a contact) defined.
type Contact = Pick<ContactWithDetails, "email"> & Partial<ContactWithDetails>;

export type ContactRow = Contact & {
  row: number;
  meetingCoHost: string;
  isUnsubscribed: string;
};

export type ContactUpload = ContactRow[];
