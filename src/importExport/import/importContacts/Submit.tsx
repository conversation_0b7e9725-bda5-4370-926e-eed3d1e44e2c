import React from "react";
import { Link as RouterLink } from "react-router-dom";
import { Box, Link, Stack, Typography } from "@mui/material";
import { MeetingDefinition, Tag } from "src/types";
import { useNewMeetingsPage } from "src/features";

export function Submit({
  meetingTemplate,
  tags,
}: {
  meetingTemplate: MeetingDefinition | null;
  tags: Tag[];
}) {
  const newMeetingsPage = useNewMeetingsPage();

  let seeMyMeetingsLink = `/instances?${
    tags.length > 0
      ? `tags=${tags.map((t) => `${t.id}:${t.name}`).join(",")}`
      : ""
  }&meetingTypes=${meetingTemplate?.id}:${
    meetingTemplate?.name
  }&categories=Guest+Email`;

  if (newMeetingsPage) {
    seeMyMeetingsLink = `/meetings?templateId=${meetingTemplate?.id}`;
  }

  return (
    <Box sx={{ width: "100%", minHeight: "400px" }}>
      <Stack spacing={2}>
        <Typography align="center">Congratulations!</Typography>
        <Typography align="center">Your upload is complete</Typography>
        <Typography align="center">
          View the status of your upload{" "}
          <Link to="/reports" component={RouterLink}>
            here
          </Link>
        </Typography>
        {meetingTemplate && (
          <Link align="center" to={seeMyMeetingsLink} component={RouterLink}>
            See my meetings
          </Link>
        )}
      </Stack>
    </Box>
  );
}

export default Submit;
