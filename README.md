# Kronologic App

Welcome to the frontend app for **Kronologic**.

# Setup & Dependencies

We use `asdf` to ensure that all developers install the exact versions of the tools required to build and run this project.

### node & yarn

1. Install [asdf](https://asdf-vm.com/#/core-manage-asdf)
2. Add `asdf` to [your shell](https://asdf-vm.com/#/core-manage-asdf?id=add-to-your-shell)
3. Add the required `asdf` plugins:

   All required plugins are located in `.tool-versions`.

   #### Add all

   ```bash
   $ cd <project-root>
   $ ./scripts/asdf-install-plugins.sh
   ```

   #### Alternative: Add manually

   Add them individualy with:

   ```bash
   $ asdf plugin add <plugin-name>
   ```

4. Install `asdf` dependencies:

   To install the exact `asdf` plugin versions required for this project, run:

   ```bash
   $ asdf install
   ```

### gpg

`yarn` depends on `gpg` to validate the authenticity of the dependencies listed in `package.json`

#### Mac

1. Install the [brew](https://brew.sh) package manager
2. Run:

   ```
   $ brew install gpg
   ```

# Env file

Copy `env_default` to `.env`

## local FE + remote BE

The frontend can be run locally and connect to any of the staging envs.

NOTE: cannot connect to prod as CORS is not configured for that env

point the services env vars at any staging env in the `.env` file

```
CHANNEL_SAMPLING_SERVICE=https://pluto.kronologic.ai/api
PROVISIONING_SERVICE=https://pluto.kronologic.ai/api
REPORTING_SERVICE=https://pluto.kronologic.ai/api
USER_SERVICE=https://pluto.kronologic.ai/api
```

# Commands

To install dependencies for the frontend app.

```bash
$ yarn install
```

To test in dev mode just start application. It will open port 8000.

```bash
$ yarn dev
```

To build for production:

```bash
$ yarn build
```

# Distribution files

Once you run `$ yarn build` you will get a untracked git folder called `dist` which will contain all the necessary files to run the app. Normally you wouldn't care for these files, since Github should be handling the processing of the distribution files and upload them to AWS.

## CSS Style

We use the BEM naming convention for css classes: http://getbem.com/naming/

## Code Style

We follow Google's Javascript style guide: https://google.github.io/styleguide/jsguide.html

## Code Review Guidelines & Checklist

Code reviews in the UI simply there to help improve code health. They do not exist to ensure code is perfect, nor slow down development.

Some guidelines:

- On matters of style the style guide is the absolute authority
- UI changes are sensible and look good
- One business day is the maximum time it should take

What to primarily look for is defined in the [Kronologic code review best practices](https://kronologic.atlassian.net/wiki/spaces/EN/pages/4718604/On+Boarding#Code-Review-Best-Practices)

What to look for at a minimum in the UI:

1. Does browser support need to be updated?
2. Was anything modified or lost in the merges?
3. Does it cause any warnings, or error in the browser console?
4. Are the UI Changes sensible?
5. Does this code do what it is supposed to do?
6. Can the solution be simplified?
7. Can you think of any use-case in which the code does not behave as intended?
8. Does anything need to be components or should existing components be used?

## Stack

- React 16.9.x
- Webpack 5.x

## Methodology

- Use of `ES6/7/8` is prefered through out the entire code repository.

- Try to avoid for loop syntax if there's a better way to do it with `map, filter, reduce`, etc... `lambda style`.

- Heavy use of `React hooks`, meaning that there are no React components unless we're dealing with Error Boundaries, in such case there's no way around it.

- Use of JS classes to create factories, and complex functionality is permitted, but should be limited to just few cases.

- The use of `pure functions` is encouraged, and the correct (sometimes wise) use of memoized functions/properties is highly encouraged, with the use of `useMemo` and `useCallback`.

- When importing packages, we need to ask ourselves, do we really need it? Are there alternatives that are lightweight? And if required, perform a profiling test before and after using library(ies) with file size deltas.

- Perform lazy loading of components when neede, using `import` for packages, `React.lazy` for components.

### E2E testing

E2E testing is covered by Cypress in a separate repo: [kronologic/e2e](https://github.com/kronologic/e2e)

### Dependencies

Before running any test, please be sure to download the following extension for Chrome, since Cypress uses iframe loader to to cross origin requests.

- Chrome Extension:
  https://chrome.google.com/webstore/detail/ignore-x-frame-headers/gleekbfjekiniecknbkamfmkohkpodhe
