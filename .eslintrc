{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": [
    "eslint:recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:@typescript-eslint/recommended",
    "prettier"
  ],
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "plugins": ["prettier"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "rules": {
    "no-unused-vars": "off",
    // Had to turn this off, because
    "@typescript-eslint/no-unused-vars": [
      "warn",
      { "varsIgnorePattern": "^React$" }
    ],
    // Disabled because all JS files are missing this, and it's too big a lift.
    // We will slowly replace this by converting to TS.
    "react/prop-types": "off",
    "@typescript-eslint/no-explicit-any": "warn",
    "react/no-unescaped-entities": "off",
    "react/react-in-jsx-scope": "off"
  },
  "globals": {
    "fetch": true,
    "gtag": true,
    "window": true,
    "document": true,
    "localStorage": true,
    "FormData": true,
    "FileReader": true,
    "Blob": true,
    "navigator": true,
    "cy": true,
    "BRANCH": true,
    "COMMIT": true,
    "DATADOG_APPLICATION_ID": true,
    "DATADOG_CLIENT_ID": true,
    "DATE": true
  }
}
