{
  "exclude": ["node_modules"],
  "include": ["src"],
  "compilerOptions": {
    "baseUrl": ".",
    "module": "esnext",
    "outDir": "./dist/",
    "target": "es6",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noFallthroughCasesInSwitch": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "noImplicitAny": true,
    "sourceMap": true
    // Ensure that .d.ts files are created by tsc, but not .js files
    // "declaration": true
    // "emitDeclarationOnly": true
  }
}
