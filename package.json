{"name": "app", "version": "1.1.0", "dependencies": {"@emotion/react": "^11.9.3", "@emotion/styled": "^11.13.0", "@mdi/js": "5.6.55", "@mdi/react": "1.6.1", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.89", "@mui/material": "^5.9.0", "@mui/styles": "^5.8.7", "@mui/utils": "^5.8.4", "@mui/x-data-grid-pro": "^5.17.21", "@mui/x-date-pickers": "^5.0.0-beta.0", "@mui/x-date-pickers-pro": "^5.0.8", "@tiptap/extension-hard-break": "^2.0.3", "@tiptap/extension-image": "^2.6.6", "@tiptap/extension-link": "^2.6.6", "@tiptap/extension-paragraph": "^2.6.6", "@tiptap/extension-text-align": "^2.6.6", "@tiptap/extension-underline": "^2.6.6", "@tiptap/pm": "^2.0.3", "@tiptap/react": "^2.6.6", "@tiptap/starter-kit": "^2.6.6", "@types/lodash.isequal": "^4.5.8", "@types/node": "^22.5.4", "@types/papaparse": "^5.3.7", "@types/react-helmet": "^6.1.11", "@types/react-swipeable-views": "^0.13.5", "@types/superagent-prefix": "^0.0.3", "dayjs": "^1.11.13", "dompurify": "^3.1.6", "dotenv": "^16.4.5", "express": "^4.19.2", "html-react-parser": "^3.0.4", "jotai": "^2.9.3", "js-cookie": "^3.0.5", "jwt-decode": "^3.1.2", "moment": "^2.27.0", "moment-timezone": "^0.5.45", "mustache": "^4.2.0", "quill-blot-formatter": "1.0.5", "quill-image-resize-module": "3.0.0", "quill-image-resize-module-react": "3.0.0", "react": "^16.8.6", "react-helmet": "^6.1.0", "react-hook-form": "^7.53.0", "react-qr-code": "^2.0.15", "react-router-dom": "^5.1.2", "superagent": "^10.1.0", "superagent-prefix": "^0.0.2", "superagent-use": "^0.1.0", "swr": "^1.2.2", "uuid": "^10.0.0"}, "description": "Kronologic web app", "devDependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.25.1", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-object-rest-spread": "^7.20.7", "@babel/plugin-proposal-optional-chaining": "^7.2.0", "@babel/plugin-syntax-dynamic-import": "^7.2.0", "@babel/plugin-transform-strict-mode": "^7.24.7", "@babel/polyfill": "^7.4.4", "@babel/preset-env": "^7.5.5", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.17.12", "@hot-loader/react-dom": "^17.0.2", "@nivo/pie": "^0.62.0", "@rehooks/component-size": "^1.0.3", "@types/dompurify": "^3.0.5", "@types/js-cookie": "^3.0.3", "@types/mustache": "^4.2.5", "@types/react": "16.14.0", "@types/react-dom": "^16.9.16", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "babel-loader": "^9.1.3", "classnames": "^2.5.1", "clean-webpack-plugin": "^4.0.0", "copy-webpack-plugin": "^10.2.4", "css-loader": "^6.7.1", "dompurify": "^3.1.6", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-webpack-plugin": "^4.2.0", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.6.0", "husky": "^9.1.5", "identity-obj-proxy": "^3.0.0", "lint-staged": "^15.2.10", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "lodash.throttle": "^4.1.1", "mini-css-extract-plugin": "^2.9.1", "nprogress": "^0.2.0", "papaparse": "^5.3.2", "prettier": "^3.3.3", "prop-types": "^15.7.2", "quill": "^1.3.7", "react-addons-perf": "^15.4.2", "react-confirm": "^0.1.27", "react-dates": "^21.8.0", "react-dom": "^16.8.6", "react-dropzone": "^10.1.6", "react-hint": "^3.2.0", "react-hot-loader": "^4.13.1", "react-modal": "^3.16.1", "react-quill": "^2.0.0", "react-select": "^5.8.0", "react-test-renderer": "^16.12.0", "react-window": "^1.8.10", "resolve": "^1.22.8", "resolve-url-loader": "^5.0.0", "sass": "^1.78.0", "sass-loader": "^12.6.0", "shortid": "^2.2.14", "style-loader": "^3.3.1", "stylelint": "^13.13.1", "stylelint-config-prettier": "^8.0.0", "stylelint-config-recommended": "^3.0.0", "stylelint-config-standard": "^22.0.0", "stylelint-scss": "^3.13.0", "ts-loader": "^9.5.1", "typescript": "^4.9.3", "url-loader": "^4.1.1", "webpack": "^5.94.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0", "webpack-manifest-plugin": "^5.0.0"}, "keywords": ["kronologic", "app", "kronos"], "license": "ISC", "main": "index.jsx", "scripts": {"build": "webpack build --progress --mode production --profile", "dev": "webpack serve --mode development", "pretty": "./node_modules/.bin/prettier --config ./.prettierrc --write \"./src/**/*.js\"", "start": "node ./server.js", "lint:ts": "tsc --noemit && prettier --check .", "format:fix": "prettier --write .", "lint:js": "tsc --noemit && eslint . --ext .js,.jsx,.ts,.tsx && prettier --check .", "lint:js:fix": "tsc --noemit && eslint . --ext .js,.jsx,.ts,.tsx --fix && prettier --write .", "lint:scss": "./node_modules/.bin/stylelint 'src/**/*.scss' --syntax scss", "lint:scss:fix": "./node_modules/.bin/stylelint 'src/**/*.scss' --syntax scss --fix", "prepare": "husky"}}