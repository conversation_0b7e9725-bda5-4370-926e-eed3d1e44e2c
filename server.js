const express = require("express");
const addConfigRoute = require("./addConfigRoute");
const addHealthEndpoint = require("./addHealthEndPoint");
const addIndexFileRoute = require("./addIndexFileRoute");

const app = express();
app.disable("x-powered-by");

const path = `${__dirname}/dist`;
const config = {
  host: "0.0.0.0",
  name: "kronologic-web-app-service",
  port: 8080,
};

app.use((req, res, next) => {
  console.log(`/${req.method}`);
  next();
});
addHealthEndpoint(app);
addConfigRoute(app);
app.use(express.static(path, { index: false }));
addIndexFileRoute(app);

app.listen(config.port, config.host, () =>
  console.log(`${config.name} running on ${config.host}:${config.port}`),
);
