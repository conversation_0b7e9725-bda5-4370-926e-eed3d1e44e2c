/* eslint-disable @typescript-eslint/no-var-requires */
const childProcess = require("child_process");
const path = require("path");
const { DefinePlugin, ProvidePlugin } = require("webpack");
const HtmlWebPackPlugin = require("html-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const CopyPlugin = require("copy-webpack-plugin");
const { CleanWebpackPlugin } = require("clean-webpack-plugin");
const ESLintPlugin = require("eslint-webpack-plugin");
const addConfigRoute = require("./addConfigRoute");
require("dotenv").config({ path: "./.env" });

// style files regexes
const cssRegex = /\.css$/;
const cssModuleRegex = /\.module\.css$/;
const sassRegex = /\.(scss|sass)$/;
const sassModuleRegex = /\.module\.(scss|sass)$/;
const getStyleLoaders = (isDev, cssOptions, preProcessor) => {
  const loaders = [
    isDev
      ? {
          loader: require.resolve("style-loader"),
        }
      : {
          loader: MiniCssExtractPlugin.loader,
        },
    {
      loader: require.resolve("css-loader"),
      options: cssOptions,
    },
  ].filter(Boolean);
  if (preProcessor) {
    loaders.push({
      loader: require.resolve(preProcessor),
      options: {
        sourceMap: isDev,
      },
    });
  }
  return loaders;
};

module.exports = (env) => {
  const isDev = env.WEBPACK_SERVE === "true";
  let plugins = [
    new DefinePlugin({
      "process.env": JSON.stringify(process.env),
    }),
    new HtmlWebPackPlugin({
      inject: true,
      template: path.resolve(__dirname, "./public", `index.html`),
    }),
    new CopyPlugin({
      patterns: [
        {
          from: path.resolve(__dirname, "./public"),
          globOptions: {
            dot: true,
            gitignore: true,
            ignore: ["**/index.html"],
          },
          to: path.resolve(__dirname, "./dist"),
        },
      ],
    }),
    new DefinePlugin({
      BRANCH: isDev
        ? JSON.stringify(
            childProcess.execSync("git branch --show-current", {
              encoding: "utf8",
            }),
          )
        : JSON.stringify(process.env.BRANCH),
      COMMIT: isDev
        ? JSON.stringify(
            childProcess.execSync("git rev-parse --short HEAD", {
              encoding: "utf8",
            }),
          )
        : JSON.stringify(process.env.COMMIT),
      DATE: isDev
        ? JSON.stringify(
            childProcess.execSync("date", {
              encoding: "utf8",
            }),
          )
        : JSON.stringify(process.env.DATE),
    }),
    new ProvidePlugin({
      "window.Quill": "quill",
      React: "react",
    }),
  ];

  if (isDev) {
    plugins = [
      ...plugins,
      new ESLintPlugin({
        lintDirtyModulesOnly: true,
      }),
    ];
  } else {
    plugins = [
      new CleanWebpackPlugin(),
      ...plugins,
      new MiniCssExtractPlugin({
        chunkFilename: "[id].[contenthash:8].chunk.css",
        filename: "[id].[contenthash:8].css",
      }),
    ];
  }

  return {
    bail: !isDev,
    context: __dirname,
    devServer: {
      client: {
        progress: true,
      },
      compress: false,
      devMiddleware: {
        index: "index.html",
      },
      headers: {
        "Access-Control-Allow-Headers":
          "X-Requested-With, content-type, Authorization",
        "Access-Control-Allow-Methods":
          "GET, POST, PUT, DELETE, PATCH, OPTIONS",
        "Access-Control-Allow-Origin": "*",
      },
      historyApiFallback: true,
      hot: "only",
      open: false,
      port: 8080,
      setupMiddlewares: (middlewares, devServer) => {
        addConfigRoute(devServer.app);
        return middlewares;
      },
      static: [
        {
          directory: path.resolve(__dirname, "./public"),
          staticOptions: {},
          watch: true,
        },
      ],
    },
    devtool: isDev ? "eval-source-map" : "source-map",
    entry: {
      kronologic: path.resolve(__dirname, "./src", "index.jsx"),
    },
    module: {
      rules: [
        {
          oneOf: [
            {
              loader: require.resolve("url-loader"),
              options: {
                limit: 10000,
              },
              test: [/\.gif$/, /\.jpe?g$/, /\.png$/],
              type: "javascript/auto",
            },
            {
              exclude: [/@babel(?:\/|\\{1,2})runtime/, /node_modules/],
              include: path.resolve(__dirname, "./src"),
              test: /\.(js|ts)x?$/,
              use: [
                {
                  loader: require.resolve("babel-loader"),
                  options: {
                    babelrc: true,
                    cacheCompression: !isDev,
                    cacheDirectory: true,
                    compact: !isDev,
                    sourceMaps: !isDev,
                  },
                },
              ],
            },
            {
              exclude: cssModuleRegex,
              sideEffects: true,
              test: cssRegex,
              use: getStyleLoaders({
                importLoaders: 1,
                mode: env.ENVIRONMENT,
                sourceMap: isDev,
              }),
            },
            {
              test: cssModuleRegex,
              use: getStyleLoaders(isDev, {
                importLoaders: 1,
                modules: {
                  localIdentName: "[name]__[local]___[hash:base64:5]",
                },
                sourceMap: isDev,
              }),
            },
            {
              exclude: sassModuleRegex,
              sideEffects: true,
              test: sassRegex,
              use: getStyleLoaders(
                isDev,
                {
                  importLoaders: 2,
                  sourceMap: isDev,
                },
                "sass-loader",
              ),
            },
            {
              test: sassModuleRegex,
              use: getStyleLoaders(
                isDev,
                {
                  importLoaders: 2,
                  modules: {
                    localIdentName: "[name]__[local]___[hash:base64:5]",
                  },
                  sourceMap: isDev,
                  url: false,
                },
                "sass-loader",
              ),
            },
            {
              test: /\.(woff(2)?|eot|otf|ttf|svg)(\?v=[0-9]\.[0-9]\.[0-9])?$/,
              type: "javascript/auto",
              use: {
                loader: require.resolve("file-loader"),
                options: {
                  name: "static/media/fonts/[name].[hash:8].[ext]",
                },
              },
            },
            {
              exclude: [
                /@babel(?:\/|\\{1,2})runtime/,
                /node_modules/,
                /requires.extensions/,
              ],
              include: path.resolve(__dirname, "./src"),

              test: /\.tsx?$/,
              use: { loader: "ts-loader" },
            },
            {
              exclude:
                /node_modules(?!\/quill-image-resize-module|quill-blot-formatter)/,
              loader: "babel-loader",
              test: /\.js$/,
            },
            // {
            //   exclude: [
            //     /\.(js|ttf|svg|eot|otf|extensions|woof(2)?)$/,
            //     /\.html$/,
            //     /\.json$/,
            //     /node_modules/,
            //   ],
            //   loader: require.resolve('file-loader'),
            //   options: {
            //     name: 'static/media/[name].[hash:8].[ext]',
            //   },
            //   type: 'javascript/auto',
            // },
            // ** STOP ** Are you adding a new loader?
            // Make sure to add the new loader(s) before the "file" loader.
          ],
        },
      ],
    },
    output: {
      chunkFilename: "[name].min.js",
      filename: !isDev ? "[name].[chunkhash:8].js" : "dev-bundle.js",
      library: "kronologic",
      libraryTarget: "umd",
      path: path.resolve(__dirname, "./dist"),
      publicPath: "/",
    },
    performance: {
      // TODO(Matt): Turn this off for production builds
      hints: false,
    },
    plugins,
    resolve: {
      alias: {
        assets: path.resolve(__dirname, "./src/assets"),
        "react-dom": "@hot-loader/react-dom",
      },
      extensions: [
        ".js",
        ".jsx",
        ".css",
        ".scss",
        ".json",
        ".csv",
        ".svg",
        ".png",
        ".jpg",
        ".jpeg",
        ".ts",
        ".tsx",
        ".module.scss",
      ],
      fallback: {
        fs: false,
      },
      modules: [path.resolve(__dirname, "./"), "./src", "./node_modules"],
      unsafeCache: true,
    },
  };
};
