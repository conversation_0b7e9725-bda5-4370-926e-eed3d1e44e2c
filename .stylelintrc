{"extends": ["stylelint-scss", "stylelint-config-recommended"], "rules": {"no-descending-specificity": null, "block-closing-brace-newline-after": "always", "block-no-empty": null, "color-no-invalid-hex": true, "comment-empty-line-before": ["always", {"ignore": ["stylelint-commands", "after-comment"]}], "declaration-colon-space-after": "always", "indentation": [2], "max-empty-lines": 1, "rule-empty-line-before": ["always", {"except": ["first-nested"], "ignore": ["after-comment"]}], "unit-whitelist": ["em", "rem", "%", "s", "px", "ms", "deg", "vh", "vw", "ch", "fr", "$base-unit"], "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "local", "export"]}], "selector-pseudo-element-no-unknown": [true, {"ignorePseudoElements": ["global", "local"]}], "at-rule-no-unknown": [true, {"ignoreAtRules": ["error", "use", "function", "if", "each", "include", "mixin", "return", "else", "for", "extend"]}], "unit-no-unknown": [true, {"ignoreUnits": ["$base-unit"]}], "property-no-unknown": [true, {"ignoreProperties": ["$base-unit", "/^BASE_/", "/^DARK_/", "/^ERROR_/", "/^LIGHT_/", "/^NEUTRAL/", "/^PRIMARY_/", "/^SECONDARY_/", "/^SUCCESS_/", "/^SHADE_/", "/^WARNING_/"]}]}}