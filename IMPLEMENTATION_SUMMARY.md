# Calendar Invite Webinar Meeting Link Restriction Implementation

## Problem

When the template invite style is "webinar", the `{{meeting_link}}` dynamic variable should not be allowed in the location field. This prevents users from accidentally including meeting links in location fields for webinar-style templates.

## Solution Implemented

### 1. Enhanced MergeFieldsSelect Component

**File:** `src/componentsV2/inputs/EmailEditor/MergeFieldsSelect.tsx`

- Added `excludeFields` prop to filter out specific merge fields
- Refactored hardcoded MenuItem elements into a dynamic array
- Filters available fields based on the `excludeFields` array

**Key Changes:**

```typescript
function MergeFieldsSelect({
  onSelect = () => null,
  excludeFields = [],
}: {
  onSelect: (value: string) => void;
  excludeFields?: string[];
});
```

### 2. Updated TemplateInputWithMergeFields Component

**File:** `src/componentsV2/inputs/TemplateInputWithMergeFields.tsx`

- Added `excludeFields` prop and passed it to `MergeFieldsSelect`
- Maintains backward compatibility with optional prop

**Key Changes:**

```typescript
function TemplateInputWithMergeFields({
  // ... other props
  excludeFields = [],
}: {
  // ... other types
  excludeFields?: string[];
});
```

### 3. Enhanced CalendarInvite Component

**File:** `src/componentsV2/CalendarInvite.tsx`

- Added automatic cleanup of `{{meeting_link}}` from location when switching to webinar
- Enhanced validation logic for webinar invite styles
- Conditionally excludes `{{meeting_link}}` from merge fields dropdown for location field

**Key Changes:**

```typescript
// Form validation to prevent manual entry of {{meeting_link}}
rules={{
  required: {
    value: true,
    message: "Invite Location is required",
  },
  validate: (value) => {
    if (isWebinar(watch("inviteStyle"))) {
      if (typeof value === "string" && value.includes("{{meeting_link}}")) {
        return "Location cannot contain {{meeting_link}} for event template types";
      }
    }
    return true;
  }
}}

// Conditional exclusion of meeting_link from dropdown
excludeFields={
  isWebinar(watch("inviteStyle"))
    ? ["{{meeting_link}}"]
    : []
}
```

## Features

### 1. Prevention

- **Dropdown Filtering**: When invite style is webinar, `{{meeting_link}}` is not shown in the merge fields dropdown for the location field
- **Form Validation**: Validates that location field doesn't contain `{{meeting_link}}` for webinar templates
- **Error Message**: Shows clear error message: "Location cannot contain {{meeting_link}} for event template types"

### 2. User Experience

- **Clear Error Messages**: Shows user-friendly validation errors when users attempt to add `{{meeting_link}}` manually
- **Intuitive Interface**: Meeting Link option simply doesn't appear in dropdown for webinar location fields

### 3. Backward Compatibility

- **Non-Webinar Templates**: All merge fields (including `{{meeting_link}}`) remain available for non-webinar invite styles
- **Optional Props**: All new props are optional, maintaining existing functionality

## How It Works

1. **User selects webinar invite style**
2. **Dropdown filtering** prevents user from selecting `{{meeting_link}}` for location
3. **Validation** catches any manual attempts to add `{{meeting_link}}`
4. **Error display** shows user-friendly error message if validation fails

## Testing the Implementation

To test this implementation:

1. **Create/Edit a meeting template**
2. **Set invite style to "Webinar"**
3. **Try to add merge fields to the location field**
4. **Verify that "Meeting Link" is not available in the dropdown**
5. **Try manually typing `{{meeting_link}}` in location field**
6. **Verify validation error appears**
7. **Switch to non-webinar invite style**
8. **Verify "Meeting Link" appears in location dropdown again**

## Benefits

- ✅ **Prevents user errors** - Users cannot accidentally add meeting links to webinar location fields
- ✅ **User-friendly error messages** - Clear validation feedback when users attempt invalid actions
- ✅ **Maintains backward compatibility** - Non-webinar templates work exactly as before
- ✅ **Intuitive interface** - Meeting Link simply doesn't appear as an option for webinar locations
- ✅ **Clean, maintainable code** - Simple, focused implementation
