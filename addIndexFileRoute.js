const fs = require("fs");
const path = require("path");
const uuid = require("uuid");

const distPath = `${__dirname}/dist`;

module.exports = function addIndexFile(app) {
  const runId = uuid.v4().replace(/-/g, "");
  const data = fs.readFileSync(path.resolve(distPath, "index.html")).toString();
  const compiled = data.replace("config.js", `config.${runId}.js`);
  app.get("*", (req, res) => {
    res.setHeader("Cache-Control", "no-cache").status(200).send(compiled);
  });
};
