require("dotenv").config();

const { REQUIRED_KEYS, OPTIONAL_KEYS } = require("./src/configuration/props");

module.exports = function addConfigRoute(app) {
  app.get("/config(.*)?.js", (req, res) => {
    const env = [...REQUIRED_KEYS, ...OPTIONAL_KEYS].reduce((acc, key) => {
      if (process.env[key]) {
        acc[key] = process.env[key];
      }
      return acc;
    }, {});
    res.status(200).send(`window.config = ${JSON.stringify(env, null, 4)}`);
  });
};
