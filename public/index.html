<!DOCTYPE html>
<html>
  <head>
    <title>Kronologic</title>
    <link
      rel="apple-touch-icon"
      sizes="57x57"
      href="/apple-icon-57x57.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="60x60"
      href="/apple-icon-60x60.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="72x72"
      href="/apple-icon-72x72.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="76x76"
      href="/apple-icon-76x76.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="114x114"
      href="/apple-icon-114x114.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="120x120"
      href="/apple-icon-120x120.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="144x144"
      href="/apple-icon-144x144.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="152x152"
      href="/apple-icon-152x152.png"
    />
    <link
      rel="apple-touch-icon"
      sizes="180x180"
      href="/apple-icon-180x180.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="192x192"
      href="/android-icon-192x192.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="32x32"
      href="/favicon-32x32.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="96x96"
      href="/favicon-96x96.png"
    />
    <link
      rel="icon"
      type="image/png"
      sizes="16x16"
      href="/favicon-16x16.png"
    />
    <link
      rel="stylesheet"
      href="https://use.typekit.net/kir7vhv.css"
    />
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
    />
    <meta name="msapplication-TileColor" content="#ffffff" />
    <meta
      name="msapplication-TileImage"
      content="/ms-icon-144x144.png"
    />
    <meta name="theme-color" content="#ffffff" />
    <meta
      content="text/html; charset=UTF-8"
      http-equiv="Content-Type"
    />
    <meta charset="utf-8" />
    <meta content="IE=edge" http-equiv="X-UA-Compatible" />
    <meta
      content="width=device-width, initial-scale=1"
      name="viewport"
    />
    <script src="/config.js"></script>

    <script type="application/javascript">

      function installAppCues(accountId) {
        window.AppcuesSettings = {
          enableURLDetection: true,
        };
        const appCuesScript = document.createElement("script");
        appCuesScript.setAttribute(
          "src", `https://fast.appcues.com/${accountId}.js`);
        document.head.appendChild(appCuesScript);
      }

      const appCuesAccountId = window?.config?.APPCUES_ACCOUNT_ID;
      if (appCuesAccountId) {
        installAppCues(appCuesAccountId);
      } else {
        console.warn("APPCUES_ACCOUNT_ID is not set");
      }

    </script>
    
  </head>
  <body data-theme="default" class="kl-body">
    <div id="kronologic-ai-app"></div>
  </body>
</html>
