name: ci

on: push

jobs:
  # gatekeeper job to know when CI is done. used by "Require status checks to pass before merging" setting
  ci_done:
    runs-on: ubuntu-latest
    needs:
      - lint_ts
      - lint_pr
      - build_containers
      - make_release
    steps:
      - run: echo "all done"

  lint_ts:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
      - run: yarn install --pure-lockfile
      - run: yarn lint:ts

  lint_pr:
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'pull_request' }}
    steps:
      - name: check for jira ticket
        uses: drmaples/pr-lint-jira@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

  build_containers:
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    outputs:
      sha_version_pr: ${{ steps.calc_sha_pr.outputs.sha_version }}
      sha_version_push: ${{ steps.calc_sha_push.outputs.sha_version }}
      sha_version: ${{ steps.push_images.outputs.sha_version }}
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - uses: actions/setup-node@v4
        with:
          node-version: "20"
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      # sha is different between `on: pull_request` and `on: push` :(
      # https://frontside.com/blog/2020-05-26-github-actions-pull_request/#how-does-pull_request-affect-actionscheckout
      - name: calc sha - pr
        id: calc_sha_pr
        if: ${{ github.event_name == 'pull_request' }}
        run: |
          SHA_DATE=$(TZ=UTC git log --no-walk HEAD --format=%ad --date=iso-local | cut -c1-10 | tr '-' '_')
          SHORT_SHA=$(echo "${{ github.event.pull_request.head.sha }}" | cut -c1-7)
          SHA_VERSION="${SHA_DATE}_${SHORT_SHA}"
          echo "SHA_VERSION: $SHA_VERSION"
          echo "sha_version=$SHA_VERSION" >> $GITHUB_OUTPUT
      - name: calc sha - push
        id: calc_sha_push
        if: ${{ github.event_name == 'push' }}
        run: |
          SHA_DATE=$(TZ=UTC git log --no-walk HEAD --format=%ad --date=iso-local | cut -c1-10 | tr '-' '_')
          SHORT_SHA=$(git log --no-walk HEAD --format=%h --abbrev=7)
          SHA_VERSION="${SHA_DATE}_${SHORT_SHA}"
          echo "SHA_VERSION: $SHA_VERSION"
          echo "sha_version=$SHA_VERSION" >> $GITHUB_OUTPUT

      - name: build and push images
        id: push_images
        env:
          DATADOG_CLIENT_ID: ${{ secrets.DATADOG_CLIENT_ID }}
        run: |
          LOCAL_BASE=kronologic
          GHCR_BASE=ghcr.io/${{ github.repository_owner }}

          # pick the right sha. PRs should take precedence
          sha_pr="${{steps.calc_sha_pr.outputs.sha_version}}"
          sha_push="${{steps.calc_sha_push.outputs.sha_version}}"
          echo "sha_pr: $sha_pr"
          echo "sha_push: $sha_push"
          SHA_VERSION=$([ "$sha_pr" != "" ] && echo $sha_pr || echo $sha_push)

          echo "GHCR_BASE: $GHCR_BASE"
          echo "SHA_VERSION: $SHA_VERSION"
          echo "sha_version=$SHA_VERSION" >> $GITHUB_OUTPUT

          SVC_UI=web

          docker build \
            --build-arg DATE="$(date)" \
            --build-arg COMMIT=$(git rev-parse --short HEAD) \
            --build-arg BRANCH=$(git branch --show-current) \
            --build-arg STAGE=${ENV} \
            --build-arg DATADOG_APPLICATION_ID=99d43815-5c7e-4f34-89f2-9fd8ea1a2bd5 \
            --build-arg DATADOG_CLIENT_ID=${DATADOG_CLIENT_ID} \
            --tag $LOCAL_BASE/$SVC_UI \
            .

          docker tag \
            $LOCAL_BASE/$SVC_UI\
            $GHCR_BASE/$SVC_UI:$SHA_VERSION

          docker push $GHCR_BASE/$SVC_UI:$SHA_VERSION

  make_release:
    runs-on: ubuntu-latest
    needs: build_containers
    if: ${{ github.ref == 'refs/heads/main' }}
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
      - uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          tag_name: release_${{ needs.build_containers.outputs.sha_version }}
          release_name: ${{ needs.build_containers.outputs.sha_version }}
          body: |
            🦑 🤖 image tag: ${{ needs.build_containers.outputs.sha_version }}
          draft: false
          prerelease: false
