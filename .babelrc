{"presets": [["@babel/preset-env", {"useBuiltIns": "usage", "corejs": "2.0.0", "shippedProposals": true, "targets": {"node": "20.17.0"}}], "@babel/preset-react", "@babel/preset-typescript"], "plugins": ["react-hot-loader/babel", "@babel/plugin-transform-strict-mode", "@babel/plugin-proposal-object-rest-spread", "@babel/plugin-proposal-class-properties", "@babel/plugin-syntax-dynamic-import", "@babel/plugin-proposal-optional-chaining", "@babel/plugin-proposal-nullish-coalescing-operator"]}