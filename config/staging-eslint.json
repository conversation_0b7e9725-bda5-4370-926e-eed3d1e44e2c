{"parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": 8, "ecmaFeatures": {"experimentalObjectRestSpread": true, "impliedStrict": true, "classes": true}}, "extends": ["airbnb", "prettier"], "plugins": ["prettier", "react-hooks", "sort-imports-es6-autofix", "sort-keys-fix", "@typescript-eslint"], "env": {"browser": true, "node": true}, "settings": {"import/resolver": {"node": {"paths": ["./src"], "extensions": [".js", ".jsx"]}, "typescript": {"paths": ["./src"], "extensions": [".ts", ".tsx"]}}}, "rules": {"comma-dangle": ["error", "always-multiline"], "jsx-a11y/label-has-for": 0, "jsx-a11y/label-has-associated-control": [2, {"controlComponents": ["CustomDropdownInput"], "depth": 3}], "react/jsx-uses-react": "error", "react/jsx-uses-vars": "error", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "error", "react/jsx-filename-extension": [1, {"extensions": [".js", ".tsx"]}], "no-param-reassign": [2, {"props": false}], "no-console": ["error"], "no-var": ["error"], "prettier/prettier": ["error"], "sort-keys-fix/sort-keys-fix": 2, "import/no-extraneous-dependencies": ["error", {"devDependencies": ["src/**/*.js", "src/__tests__/**", "**/__tests__/**", "*.js"], "optionalDependencies": false, "peerDependencies": false}], "import/prefer-default-export": 0, "import": 0, "func-names": "off", "object-shorthand": "warn", "space-before-function-paren": 0, "max-len": 0, "import/extensions": 0, "no-underscore-dangle": 0, "consistent-return": 0, "react/react-in-jsx-scope": 0, "react/prefer-stateless-function": [1, {"ignorePureComponents": true}], "react/forbid-prop-types": 0, "react/prop-types": 0, "react/no-unescaped-entities": 0, "jsx-a11y/accessible-emoji": 0, "radix": 0, "no-shadow": [2, {"hoist": "all", "allow": ["resolve", "reject", "done", "next", "err", "error"]}], "quotes": [2, "single", {"avoidEscape": true, "allowTemplateLiterals": true}]}, "globals": {"fetch": true, "gtag": true, "window": true, "document": true, "localStorage": true, "FormData": true, "FileReader": true, "Blob": true, "navigator": true, "cy": true, "BRANCH": true, "COMMIT": true, "DATADOG_APPLICATION_ID": true, "DATADOG_CLIENT_ID": true, "DATE": true}}