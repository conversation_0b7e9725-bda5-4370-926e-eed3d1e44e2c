FROM node:20.17.0-alpine as build

LABEL org.opencontainers.image.source https://github.com/kronologic/web

ARG STAGE=staging
ARG BRANCH
ARG COMMIT
ARG DATE
ARG DATADOG_APPLICATION_ID
ARG DATADOG_CLIENT_ID
ENV BRANCH=$BRANCH
ENV COMMIT=$COMMIT
ENV DATE=$DATE
ENV DATADOG_APPLICATION_ID=$DATADOG_APPLICATION_ID
ENV DATADOG_CLIENT_ID=$DATADOG_CLIENT_ID

RUN mkdir -p /app
RUN chown node /app
RUN chmod -R 700 /app
USER node

WORKDIR /app

ENV PATH /app/node_modules/.bin:$PATH

COPY ./package.json /app/.
COPY ./yarn.lock /app/.
# Copy source code
COPY . /app

USER node

RUN yarn install --pure-lockfile && \
  yarn build && \
  rm -R ./node_modules/ && \
  yarn install --pure-lockfile --production && \
  rm yarn.lock && \
  yarn cache clean

USER root
EXPOSE 8080

CMD [ "yarn", "start" ]
