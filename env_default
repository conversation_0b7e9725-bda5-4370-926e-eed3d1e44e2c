# Required
ENVIRONMENT=localhost

CHANNEL_SAMPLING_SERVICE=http://localhost:8005/api
PROVISIONING_SERVICE=http://localhost:8002/api
REPORTING_SERVICE=http://localhost:8003/api
USER_SERVICE=http://localhost:8001/api

# Optional
# APPCUES_ACCOUNT_ID=105269
# DATADOG_APPLICATION_ID=99d43815-5c7e-4f34-89f2-9fd8ea1a2bd5
# DATADOG_CLIENT_ID=pub27dbac552ce9d6008a8794d024e94ff5
# GTM_CONTAINER_ID=GTM-N7M934L
# POLLING_INTERVAL=6000
# DEDUPE_INTERVAL=4000
# SEARCH_DEBOUNCE_INTERVAL=1200

## local FE to remote BE
# CHANNEL_SAMPLING_SERVICE=https://pluto.kronologic.ai/api
# PROVISIONING_SERVICE=https://pluto.kronologic.ai/api
# REPORTING_SERVICE=https://pluto.kronologic.ai/api
# USER_SERVICE=https://pluto.kronologic.ai/api

APPLICATION_MODE=enterprise